'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { orderInfoFormSchema, type OrderInfoFormInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface OrderInfoFormProps {
  orderInfo?: any
  onSuccess?: () => void
  onCancel?: () => void
}

export function OrderInfoForm({ orderInfo, onSuccess, onCancel }: OrderInfoFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: orderInfo ? {
      orderNumber: orderInfo.orderNumber,
      affId: orderInfo.affId,
      offerId: orderInfo.offerId,
      amount: orderInfo.amount,
      currency: orderInfo.currency || 'USD',
      status: orderInfo.status || 'pending',
      customerInfo: orderInfo.customerInfo || {},
    } : {
      currency: 'USD',
      status: 'pending',
    },
  })

  const onSubmit = async (data: any) => {
    try {
      setIsLoading(true)
      setError('')

      // Validate with Zod
      const validatedData = orderInfoFormSchema.parse({
        ...data,
        currency: data.currency || 'USD',
        status: data.status || 'pending'
      })

      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const url = orderInfo ? `/api/order-infos/${orderInfo._id}` : '/api/order-infos'
      const method = orderInfo ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(validatedData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Operation failed')
      }

      if (result.success) {
        onSuccess?.()
      } else {
        throw new Error(result.message || 'Operation failed')
      }
    } catch (error: any) {
      setError(error.message || 'Operation failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{orderInfo ? 'Edit Order Info' : 'Create New Order Info'}</CardTitle>
        <CardDescription>
          {orderInfo ? 'Update order information' : 'Enter order information'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="orderNumber" className="text-sm font-medium">
                Order Number *
              </label>
              <Input
                id="orderNumber"
                placeholder="Enter order number"
                {...register('orderNumber')}
                className={errors.orderNumber ? 'border-red-500' : ''}
              />
              {errors.orderNumber && (
                <p className="text-sm text-red-500">{errors.orderNumber.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="affId" className="text-sm font-medium">
                Affiliate ID *
              </label>
              <Input
                id="affId"
                placeholder="Enter affiliate ID"
                {...register('affId')}
                className={errors.affId ? 'border-red-500' : ''}
              />
              {errors.affId && (
                <p className="text-sm text-red-500">{errors.affId.message as string}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="offerId" className="text-sm font-medium">
                Offer ID *
              </label>
              <Input
                id="offerId"
                placeholder="Enter offer ID"
                {...register('offerId')}
                className={errors.offerId ? 'border-red-500' : ''}
              />
              {errors.offerId && (
                <p className="text-sm text-red-500">{errors.offerId.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="amount" className="text-sm font-medium">
                Amount *
              </label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...register('amount', { valueAsNumber: true })}
                className={errors.amount ? 'border-red-500' : ''}
              />
              {errors.amount && (
                <p className="text-sm text-red-500">{errors.amount.message as string}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="currency" className="text-sm font-medium">
                Currency
              </label>
              <Input
                id="currency"
                placeholder="USD"
                {...register('currency')}
                className={errors.currency ? 'border-red-500' : ''}
              />
              {errors.currency && (
                <p className="text-sm text-red-500">{errors.currency.message as string}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="status" className="text-sm font-medium">
                Status
              </label>
              <select
                id="status"
                {...register('status')}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="cancelled">Cancelled</option>
                <option value="refunded">Refunded</option>
              </select>
              {errors.status && (
                <p className="text-sm text-red-500">{errors.status.message as string}</p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Customer Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="customerEmail" className="text-sm font-medium">
                  Email
                </label>
                <Input
                  id="customerEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('customerInfo.email')}
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="customerPhone" className="text-sm font-medium">
                  Phone
                </label>
                <Input
                  id="customerPhone"
                  placeholder="+1234567890"
                  {...register('customerInfo.phone')}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="customerFirstName" className="text-sm font-medium">
                  First Name
                </label>
                <Input
                  id="customerFirstName"
                  placeholder="John"
                  {...register('customerInfo.firstName')}
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="customerLastName" className="text-sm font-medium">
                  Last Name
                </label>
                <Input
                  id="customerLastName"
                  placeholder="Doe"
                  {...register('customerInfo.lastName')}
                />
              </div>
            </div>
          </div>

          {error && (
            <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="flex gap-2 justify-end">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : orderInfo ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
