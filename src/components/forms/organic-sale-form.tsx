'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { organicSaleSchema, type OrganicSaleInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface OrganicSaleFormProps {
  organicSale?: any
  onSuccess?: () => void
  onCancel?: () => void
}

export function OrganicSaleForm({ organicSale, onSuccess, onCancel }: OrganicSaleFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<OrganicSaleInput>({
    resolver: zodResolver(organicSaleSchema),
    defaultValues: organicSale ? {
      pageURL: organicSale.pageURL,
      affId: organicSale.affId,
      offerId: organicSale.offerId,
    } : undefined,
  })

  const onSubmit = async (data: OrganicSaleInput) => {
    try {
      setIsLoading(true)
      setError('')

      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const url = organicSale ? `/api/organic-sales/${organicSale._id}` : '/api/organic-sales'
      const method = organicSale ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Operation failed')
      }

      if (result.success) {
        onSuccess?.()
      } else {
        throw new Error(result.message || 'Operation failed')
      }
    } catch (error: any) {
      setError(error.message || 'Operation failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{organicSale ? 'Edit Organic Sale' : 'Create New Organic Sale'}</CardTitle>
        <CardDescription>
          {organicSale ? 'Update organic sale information' : 'Enter organic sale information'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="pageURL" className="text-sm font-medium">
              Page URL *
            </label>
            <Input
              id="pageURL"
              type="url"
              placeholder="https://example.com/page"
              {...register('pageURL')}
              className={errors.pageURL ? 'border-red-500' : ''}
            />
            {errors.pageURL && (
              <p className="text-sm text-red-500">{errors.pageURL.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="affId" className="text-sm font-medium">
                Affiliate ID *
              </label>
              <Input
                id="affId"
                placeholder="Enter affiliate ID"
                {...register('affId')}
                className={errors.affId ? 'border-red-500' : ''}
              />
              {errors.affId && (
                <p className="text-sm text-red-500">{errors.affId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="offerId" className="text-sm font-medium">
                Offer ID *
              </label>
              <Input
                id="offerId"
                placeholder="Enter offer ID"
                {...register('offerId')}
                className={errors.offerId ? 'border-red-500' : ''}
              />
              {errors.offerId && (
                <p className="text-sm text-red-500">{errors.offerId.message}</p>
              )}
            </div>
          </div>

          {error && (
            <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="flex gap-2 justify-end">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : organicSale ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
