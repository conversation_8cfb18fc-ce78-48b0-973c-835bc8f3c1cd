'use client'

import { useState } from 'react'
import { z } from 'zod'
import Card from 'react-bootstrap/Card'
import Form from 'react-bootstrap/Form'
import But<PERSON> from 'react-bootstrap/Button'
import Alert from 'react-bootstrap/Alert'
import { api } from '@/lib/api-client'

// Registration schema
const registerSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

interface RegisterFormData {
  firstName: string
  lastName: string
  email: string
  password: string
}

interface RegisterFormProps {
  onSwitchToLogin?: () => void
}

export function RegisterForm({ onSwitchToLogin }: RegisterFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setIsLoading(true)
      setError('')
      setSuccess('')

      // Validate form data
      const validatedData = registerSchema.parse(formData)
      const result = await api.post('/api/auth/register', validatedData)

      if (result.success) {
        setSuccess('Registration successful! Please login with your credentials.')
        setTimeout(() => {
          onSwitchToLogin?.()
        }, 2000)
      } else {
        throw new Error(result.message || 'Registration failed')
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        setError(error.errors[0].message)
      } else {
        setError(error instanceof Error ? error.message : 'Registration failed')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
      <div style={{ maxWidth: 400, width: '100%' }}>
        <Card>
          <Card.Body className="p-4">
            <div className="text-center">
              <h1 className="h3 fw-bold text-dark">Create Account</h1>
              <p className="mt-2 text-muted small">
                Join Global Performance Commerce Management System
              </p>
            </div>

            {error && (
              <Alert variant="danger" className="mb-3">
                {error}
              </Alert>
            )}

            {success && (
              <Alert variant="success" className="mb-3">
                {success}
              </Alert>
            )}

            <Form onSubmit={handleSubmit}>
              <div className="row">
                <div className="col-md-6">
                  <Form.Group className="mb-3">
                    <Form.Label>First Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="firstName"
                      placeholder="Enter first name"
                      value={formData.firstName}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                </div>
                <div className="col-md-6">
                  <Form.Group className="mb-3">
                    <Form.Label>Last Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="lastName"
                      placeholder="Enter last name"
                      value={formData.lastName}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                </div>
              </div>

              <Form.Group className="mb-3">
                <Form.Label>Email</Form.Label>
                <Form.Control
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  autoComplete="email"
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Password</Form.Label>
                <Form.Control
                  type="password"
                  name="password"
                  placeholder="Enter your password (min 6 characters)"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  autoComplete="new-password"
                />
              </Form.Group>

              <Button
                variant="primary"
                type="submit"
                disabled={isLoading}
                className="w-100 py-2"
                size="lg"
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Button>
            </Form>

            {onSwitchToLogin && (
              <div className="text-center mt-3">
                <p className="text-muted">
                  Already have an account?{' '}
                  <button
                    type="button"
                    onClick={onSwitchToLogin}
                    className="btn btn-link p-0 text-decoration-none"
                    style={{ verticalAlign: 'baseline' }}
                  >
                    Sign in here
                  </button>
                </p>
              </div>
            )}
          </Card.Body>
        </Card>
      </div>
    </div>
  )
}
