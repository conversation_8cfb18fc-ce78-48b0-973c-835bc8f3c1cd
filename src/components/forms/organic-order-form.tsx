'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { organicOrderSchema, type OrganicOrderInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface OrganicOrderFormProps {
  organicOrder?: any
  onSuccess?: () => void
  onCancel?: () => void
}

export function OrganicOrderForm({ organicOrder, onSuccess, onCancel }: OrganicOrderFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<OrganicOrderInput>({
    resolver: zodResolver(organicOrderSchema),
    defaultValues: organicOrder ? {
      affId: organicOrder.affId,
      offerId: organicOrder.offerId,
      orderInfo: organicOrder.orderInfo,
    } : undefined,
  })

  const onSubmit = async (data: OrganicOrderInput) => {
    try {
      setIsLoading(true)
      setError('')

      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const url = organicOrder ? `/api/organic-orders/${organicOrder._id}` : '/api/organic-orders'
      const method = organicOrder ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Operation failed')
      }

      if (result.success) {
        onSuccess?.()
      } else {
        throw new Error(result.message || 'Operation failed')
      }
    } catch (error: any) {
      setError(error.message || 'Operation failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{organicOrder ? 'Edit Organic Order' : 'Create New Organic Order'}</CardTitle>
        <CardDescription>
          {organicOrder ? 'Update organic order information' : 'Enter organic order information'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="affId" className="text-sm font-medium">
                Affiliate ID *
              </label>
              <Input
                id="affId"
                placeholder="Enter affiliate ID"
                {...register('affId')}
                className={errors.affId ? 'border-red-500' : ''}
              />
              {errors.affId && (
                <p className="text-sm text-red-500">{errors.affId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="offerId" className="text-sm font-medium">
                Offer ID *
              </label>
              <Input
                id="offerId"
                placeholder="Enter offer ID"
                {...register('offerId')}
                className={errors.offerId ? 'border-red-500' : ''}
              />
              {errors.offerId && (
                <p className="text-sm text-red-500">{errors.offerId.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Order Information</h3>
            
            <div className="space-y-2">
              <label htmlFor="orderNumber" className="text-sm font-medium">
                Order Number *
              </label>
              <Input
                id="orderNumber"
                placeholder="Enter order number"
                {...register('orderInfo.orderNumber')}
                className={errors.orderInfo?.orderNumber ? 'border-red-500' : ''}
              />
              {errors.orderInfo?.orderNumber && (
                <p className="text-sm text-red-500">{errors.orderInfo.orderNumber.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="clickEvent" className="text-sm font-medium">
                Click Event *
              </label>
              <Input
                id="clickEvent"
                placeholder="Enter click event"
                {...register('orderInfo.clickEvent')}
                className={errors.orderInfo?.clickEvent ? 'border-red-500' : ''}
              />
              {errors.orderInfo?.clickEvent && (
                <p className="text-sm text-red-500">{errors.orderInfo.clickEvent.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="transactionId" className="text-sm font-medium">
                  Transaction ID *
                </label>
                <Input
                  id="transactionId"
                  placeholder="Enter transaction ID"
                  {...register('orderInfo.conversionEvent.transaction_id')}
                  className={errors.orderInfo?.conversionEvent?.transaction_id ? 'border-red-500' : ''}
                />
                {errors.orderInfo?.conversionEvent?.transaction_id && (
                  <p className="text-sm text-red-500">{errors.orderInfo.conversionEvent.transaction_id.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="conversionId" className="text-sm font-medium">
                  Conversion ID *
                </label>
                <Input
                  id="conversionId"
                  placeholder="Enter conversion ID"
                  {...register('orderInfo.conversionEvent.conversion_id')}
                  className={errors.orderInfo?.conversionEvent?.conversion_id ? 'border-red-500' : ''}
                />
                {errors.orderInfo?.conversionEvent?.conversion_id && (
                  <p className="text-sm text-red-500">{errors.orderInfo.conversionEvent.conversion_id.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="reffererUrl" className="text-sm font-medium">
                Referrer URL *
              </label>
              <Input
                id="reffererUrl"
                type="url"
                placeholder="https://example.com/referrer"
                {...register('orderInfo.reffererUrl')}
                className={errors.orderInfo?.reffererUrl ? 'border-red-500' : ''}
              />
              {errors.orderInfo?.reffererUrl && (
                <p className="text-sm text-red-500">{errors.orderInfo.reffererUrl.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="url" className="text-sm font-medium">
                Landing URL *
              </label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com/landing"
                {...register('orderInfo.url')}
                className={errors.orderInfo?.url ? 'border-red-500' : ''}
              />
              {errors.orderInfo?.url && (
                <p className="text-sm text-red-500">{errors.orderInfo.url.message}</p>
              )}
            </div>
          </div>

          {error && (
            <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="flex gap-2 justify-end">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : organicOrder ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
