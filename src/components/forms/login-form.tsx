'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth-context'
import Card from 'react-bootstrap/Card'
import Form from 'react-bootstrap/Form'
import Button from 'react-bootstrap/Button'
import Alert from 'react-bootstrap/Alert'

interface LoginFormData {
  email: string
  password: string
}

interface LoginFormProps {
  onSwitchToRegister?: () => void
}

export function LoginForm({ onSwitchToRegister }: LoginFormProps) {
  const { login } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setIsLoading(true)
      setError('')
      console.log('Login form - attempting login with:', formData.email)
      await login(formData.email, formData.password)
      console.log('Login form - login successful')
    } catch (error: any) {
      console.log('Login form - login error:', error)
      setError(error.message || 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
      <div style={{ maxWidth: 400, width: '100%' }}>
        <Card>
          <Card.Body className="p-4">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900">Everflow Manager</h1>
              <p className="mt-2 text-sm text-gray-600">
                Global Performance Commerce Management System
              </p>
            </div>

            {error && (
              <Alert variant="danger" className="mb-3">
                {error}
              </Alert>
            )}

            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>Email</Form.Label>
                <Form.Control
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  autoComplete="email"
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Password</Form.Label>
                <Form.Control
                  type="password"
                  name="password"
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  autoComplete="current-password"
                />
              </Form.Group>

              <Button
                variant="primary"
                type="submit"
                disabled={isLoading}
                className="w-100 py-2"
                size="lg"
              >
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </Form>

            <div className="text-center mt-3">
              <small className="text-muted">
                Demo credentials: <EMAIL> / test123
              </small>
            </div>

            {onSwitchToRegister && (
              <div className="text-center mt-3">
                <p className="text-muted">
                  Don't have an account?{' '}
                  <button
                    type="button"
                    onClick={onSwitchToRegister}
                    className="btn btn-link p-0 text-decoration-none"
                    style={{ verticalAlign: 'baseline' }}
                  >
                    Create one here
                  </button>
                </p>
              </div>
            )}
          </Card.Body>
        </Card>
      </div>
    </div>
  )
}
