'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { affiliateSchema, type AffiliateInput, type AltAffInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { api } from '@/lib/api-client'



interface AffiliateFormProps {
  affiliate?: {
    _id?: string
    affId: string
    offerId: string
    altAffId?: string
    altAffs?: AltAffInput[]
    rate?: number
    orders?: number
    total_not_fired?: number
    startDate?: string
    endDate?: string
    userEmail?: string
    isPaused?: boolean
  }
  onSuccess?: () => void
  onCancel?: () => void
}

export function AffiliateForm({ affiliate, onSuccess, onCancel }: AffiliateFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [newAltAff, setNewAltAff] = useState({ altAffId: '', rate: 0, isPaused: false })

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    resolver: zodResolver(affiliateSchema),
    defaultValues: affiliate ? {
      affId: affiliate.affId,
      offerId: affiliate.offerId,
      altAffId: affiliate.altAffId || '',
      altAffs: affiliate.altAffs || [],
      rate: affiliate.rate || 0,
      orders: affiliate.orders || 0,
      total_not_fired: affiliate.total_not_fired || 0,
      startDate: affiliate.startDate ? new Date(affiliate.startDate).toISOString().split('T')[0] : '',
      endDate: affiliate.endDate ? new Date(affiliate.endDate).toISOString().split('T')[0] : '',
      userEmail: affiliate.userEmail || '',
      isPaused: affiliate.isPaused || false,
    } : {
      affId: '',
      offerId: '',
      altAffId: '',
      rate: 0,
      orders: 0,
      total_not_fired: 0,
      startDate: '',
      endDate: '',
      altAffs: [],
      isPaused: false,
      userEmail: '',
    },
  })

  const altAffs = watch('altAffs') || []
  const affiliateRate = watch('rate') || 0

  // Calculate total rate of AltAffs
  const totalAltAffRate = altAffs.reduce((sum, altAff) => sum + (altAff.rate || 0), 0)
  const rateWarning = totalAltAffRate !== affiliateRate && altAffs.length > 0

  // Get current user email
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const result = await api.get('/api/auth/me')
        if (result.success && !affiliate) {
          const userData = result.data as any
          setValue('userEmail', userData?.email || '')
        }
      } catch (error) {
        console.error('Error fetching current user:', error)
        // Error will be handled by api client (auto logout if 401)
      }
    }

    fetchCurrentUser()
  }, [affiliate, setValue])

  // Functions to manage altAffs array
  const addAltAff = () => {
    if (newAltAff.altAffId.trim()) {
      const currentAltAffs = altAffs || []
      const exists = currentAltAffs.some(item => item.altAffId === newAltAff.altAffId.trim())
      if (!exists) {
        setValue('altAffs', [...currentAltAffs, { ...newAltAff, altAffId: newAltAff.altAffId.trim() }])
        setNewAltAff({ altAffId: '', rate: 0, isPaused: false })
      }
    }
  }

  const removeAltAff = (index: number) => {
    const currentAltAffs = altAffs || []
    const newAltAffs = currentAltAffs.filter((_, i) => i !== index)
    setValue('altAffs', newAltAffs)
  }

  const toggleAltAffPause = (index: number) => {
    const currentAltAffs = altAffs || []
    const newAltAffs = [...currentAltAffs]
    newAltAffs[index] = { ...newAltAffs[index], isPaused: !newAltAffs[index].isPaused }
    setValue('altAffs', newAltAffs)
  }

  const onSubmit = async (data: any) => {
    try {
      setIsLoading(true)
      setError('')

      // Clean up data before sending
      const cleanData = {
        ...data,
        startDate: data.startDate || undefined,
        endDate: data.endDate || undefined,
        altAffs: data.altAffs || []
      }



      // Validate rate matching if AltAffs exist
      if (cleanData.altAffs && cleanData.altAffs.length > 0) {
        const totalAltAffRate = cleanData.altAffs.reduce((sum: number, altAff: any) => sum + (altAff.rate || 0), 0)
        if (Math.abs(totalAltAffRate - (cleanData.rate || 0)) > 0.01) { // Allow small floating point differences
          setError(`Total AltAff rates (${totalAltAffRate.toFixed(2)}%) must equal Affiliate rate (${(cleanData.rate || 0).toFixed(2)}%)`)
          return
        }
      }

      const url = affiliate ? `/api/affiliates/${affiliate._id}` : '/api/affiliates'

      const result = affiliate
        ? await api.put(url, cleanData)
        : await api.post(url, cleanData)



      if (result.success) {
        onSuccess?.()
      } else {
        throw new Error(result.message || 'Operation failed')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Operation failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{affiliate ? 'Edit Affiliate' : 'Create New Affiliate'}</CardTitle>
        <CardDescription>
          {affiliate ? 'Update affiliate information' : 'Enter affiliate information'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <label htmlFor="affId" className="text-sm font-medium text-gray-700">
                Affiliate ID *
              </label>
              <Input
                id="affId"
                placeholder="Enter affiliate ID"
                {...register('affId')}
                className={`transition-colors ${errors.affId ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'}`}
              />
              {errors.affId && (
                <p className="text-sm text-red-500">{errors.affId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="offerId" className="text-sm font-medium">
                Offer ID *
              </label>
              <Input
                id="offerId"
                placeholder="Enter offer ID"
                {...register('offerId')}
                className={errors.offerId ? 'border-red-500' : ''}
              />
              {errors.offerId && (
                <p className="text-sm text-red-500">{errors.offerId.message}</p>
              )}
            </div>
          </div>

          {/* User email is auto-filled but hidden from UI */}
          <input type="hidden" {...register('userEmail')} />

          <div className="space-y-2">
            <label htmlFor="altAffId" className="text-sm font-medium">
              Alternate Affiliate ID {altAffs.length === 0 && '*'}
            </label>
            <Input
              id="altAffId"
              placeholder="Enter alternate affiliate ID"
              {...register('altAffId')}
              className={errors.altAffId ? 'border-red-500' : ''}
            />
            {errors.altAffId && (
              <p className="text-sm text-red-500">{errors.altAffId.message}</p>
            )}
            <p className="text-xs text-gray-500">
              Required if no alternate affiliate IDs are added below
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium text-gray-700">
                Alternate Affiliate IDs (Multiple)
              </label>
              {altAffs.length > 0 && (
                <span className={`text-xs px-3 py-1 rounded-full font-medium ${
                  rateWarning ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' : 'bg-green-100 text-green-800 border border-green-200'
                }`}>
                  Total: {totalAltAffRate.toFixed(2)}%
                </span>
              )}
            </div>
            <div className="grid grid-cols-4 gap-2">
              <Input
                placeholder="Alt Aff ID"
                value={newAltAff.altAffId}
                onChange={(e) => setNewAltAff(prev => ({ ...prev, altAffId: e.target.value }))}
              />
              <Input
                type="number"
                placeholder="Rate (%)"
                value={newAltAff.rate}
                onChange={(e) => setNewAltAff(prev => ({ ...prev, rate: parseFloat(e.target.value) || 0 }))}
              />
              <div className="flex items-center gap-2">
                <div className="form-check form-switch">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="newAltAffPaused"
                    checked={newAltAff.isPaused}
                    onChange={(e) => setNewAltAff(prev => ({ ...prev, isPaused: e.target.checked }))}
                  />
                  <label htmlFor="newAltAffPaused" className="form-check-label text-sm">Paused</label>
                </div>
              </div>
              <Button type="button" onClick={addAltAff} variant="outline">
                Add
              </Button>
            </div>

            {altAffs.length > 0 && (
              <div className="space-y-2">
                <p className="text-xs text-gray-500">Added alternate affiliates:</p>
                <div className="space-y-2">
                  {altAffs.map((altAff, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-3 rounded-md border ${
                        altAff.isPaused ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'
                      }`}
                    >
                      <div className="flex items-center gap-4">
                        <span className="font-medium">{altAff.altAffId}</span>
                        <span className="text-sm text-gray-600">Rate: {altAff.rate}%</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          altAff.isPaused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {altAff.isPaused ? 'Paused' : 'Active'}
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => toggleAltAffPause(index)}
                        >
                          {altAff.isPaused ? 'Restore' : 'Pause'}
                        </Button>
                        <Button
                          type="button"
                          size="sm"
                          variant="destructive"
                          onClick={() => removeAltAff(index)}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Rate validation warning */}
            {rateWarning && (
              <div className="mt-4 p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded-r-md shadow-sm">
                <div className="flex items-start gap-3">
                  <i className="bi bi-exclamation-triangle text-yellow-600 text-lg mt-0.5"></i>
                  <div className="text-sm">
                    <p className="font-semibold text-yellow-800 mb-1">Rate Mismatch Warning</p>
                    <p className="text-yellow-700">
                      Total AltAff rates ({totalAltAffRate.toFixed(2)}%) should equal Affiliate rate ({affiliateRate.toFixed(2)}%)
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <label htmlFor="rate" className="text-sm font-medium">
                Rate (%)
              </label>
              <Input
                id="rate"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...register('rate', { valueAsNumber: true })}
                className={errors.rate ? 'border-red-500' : ''}
              />
              {errors.rate && (
                <p className="text-sm text-red-500">{errors.rate.message}</p>
              )}
              {altAffs.length > 0 && (
                <p className={`text-xs ${
                  rateWarning ? 'text-yellow-600' : 'text-green-600'
                }`}>
                  AltAffs total: {totalAltAffRate.toFixed(2)}%
                  {rateWarning ? ' (mismatch!)' : ' (matches)'}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="orders" className="text-sm font-medium">
                Orders
              </label>
              <Input
                id="orders"
                type="number"
                placeholder="0"
                {...register('orders', { valueAsNumber: true })}
                className={errors.orders ? 'border-red-500' : ''}
              />
              {errors.orders && (
                <p className="text-sm text-red-500">{errors.orders.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="total_not_fired" className="text-sm font-medium">
                Total Not Fired
              </label>
              <Input
                id="total_not_fired"
                type="number"
                placeholder="0"
                {...register('total_not_fired', { valueAsNumber: true })}
                className={errors.total_not_fired ? 'border-red-500' : ''}
              />
              {errors.total_not_fired && (
                <p className="text-sm text-red-500">{errors.total_not_fired.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="startDate" className="text-sm font-medium">
                Start Date *
              </label>
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
                className={errors.startDate ? 'border-red-500' : ''}
              />
              {errors.startDate && (
                <p className="text-sm text-red-500">{errors.startDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="endDate" className="text-sm font-medium">
                End Date *
              </label>
              <Input
                id="endDate"
                type="date"
                {...register('endDate')}
                className={errors.endDate ? 'border-red-500' : ''}
              />
              {errors.endDate && (
                <p className="text-sm text-red-500">{errors.endDate.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="form-check form-switch">
              <input
                type="checkbox"
                className="form-check-input"
                id="affiliatePaused"
                {...register('isPaused')}
              />
              <label htmlFor="affiliatePaused" className="form-check-label text-sm font-medium">Paused</label>
            </div>
          </div>

          {error && (
            <div className="p-4 bg-red-50 border-l-4 border-red-400 rounded-r-md shadow-sm">
              <div className="flex items-start gap-3">
                <i className="bi bi-exclamation-circle text-red-600 text-lg mt-0.5"></i>
                <div className="text-sm text-red-700">
                  {error}
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-3 justify-end pt-4 border-t border-gray-200">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="px-6 py-2 border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
            )}

            <Button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <i className="bi bi-arrow-clockwise animate-spin mr-2"></i>
                  Saving...
                </>
              ) : (
                affiliate ? 'Update Affiliate' : 'Create Affiliate'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
