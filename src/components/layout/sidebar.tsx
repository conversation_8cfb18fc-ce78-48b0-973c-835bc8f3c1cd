'use client'

import { useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import Nav from 'react-bootstrap/Nav'
import Button from 'react-bootstrap/Button'
import Dropdown from 'react-bootstrap/Dropdown'
import Link from 'next/link'

const navigation = [
  {
    key: '/dashboard',
    icon: 'bi-house-fill',
    label: 'Dashboard',
    href: '/dashboard'
  },
  {
    key: '/dashboard/users',
    icon: 'bi-people-fill',
    label: 'Users',
    href: '/dashboard/users'
  },
  {
    key: '/affiliates',
    icon: 'bi-diagram-3',
    label: 'Affiliates',
    href: '/affiliates'
  },
  {
    key: '/organic-sales',
    icon: 'bi-cart-check',
    label: 'Organic Sales',
    href: '/organic-sales'
  },
  {
    key: '/organic-orders',
    icon: 'bi-box-seam',
    label: 'Organic Orders',
    href: '/organic-orders'
  },
  {
    key: '/order-infos',
    icon: 'bi-file-text',
    label: 'Order Infos',
    href: '/order-infos'
  },
  {
    key: '/error-logs',
    icon: 'bi-exclamation-triangle-fill',
    label: 'Error Logs',
    href: '/error-logs'
  },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { user, logout } = useAuth()
  const [collapsed, setCollapsed] = useState(false)

  return (
    <div
      className={`d-flex flex-column text-white h-100 ${className}`}
      style={{
        width: collapsed ? '80px' : '100%',
        minWidth: collapsed ? '80px' : '200px',
        maxWidth: collapsed ? '80px' : '280px',
        transition: 'width 0.2s ease'
      }}
    >
      {/* Header */}
      <div className="p-3 border-bottom border-secondary">
        <div className="d-flex align-items-center justify-content-between">
          {!collapsed && (
            <h5 className="mb-0 text-white">
              <i className="bi bi-building me-2"></i>
              GPC Everflow
            </h5>
          )}
          <Button
            variant="outline-light"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className="border-0"
          >
            <i className={`bi ${collapsed ? 'bi-chevron-right' : 'bi-chevron-left'}`}></i>
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <Nav className="flex-column flex-grow-1 p-2">
        {navigation.map((item) => (
          <Nav.Item key={item.key} className="mb-1">
            <Link
              href={item.href}
              className={`nav-link text-white d-flex align-items-center p-3 rounded ${
                pathname === item.key ? 'bg-primary' : 'hover-bg-secondary'
              }`}
              style={{ textDecoration: 'none' }}
            >
              <i className={`bi ${item.icon} me-3`} style={{ fontSize: '1.2em' }}></i>
              {!collapsed && (
                <span>{item.label}</span>
              )}
            </Link>
          </Nav.Item>
        ))}
      </Nav>

      {/* User Section */}
      <div className="p-3 border-top border-secondary">
        {!collapsed ? (
          <div>
            {/* User Info */}
            <div className="d-flex align-items-center mb-3">
              <div
                className="rounded-circle bg-primary d-flex align-items-center justify-content-center me-3"
                style={{ width: '40px', height: '40px' }}
              >
                <i className="bi bi-person-fill text-white"></i>
              </div>
              <div className="text-start">
                <div className="fw-bold text-white">{user?.email || 'User'}</div>
                <small className="text-white">Administrator</small>
              </div>
            </div>

            {/* Logout Button */}
            <Button
              variant="outline-danger"
              size="sm"
              onClick={logout}
              className="w-100 d-flex align-items-center justify-content-center"
            >
              <i className="bi bi-box-arrow-right me-2"></i>
              Logout
            </Button>
          </div>
        ) : (
          <div className="text-center">
            <Button
              variant="outline-danger"
              size="sm"
              onClick={logout}
              className="w-100 d-flex align-items-center justify-content-center"
              title="Logout"
            >
              <i className="bi bi-box-arrow-right"></i>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
