'use client'

import { useAuth } from '@/lib/auth-context'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect } from 'react'

import Spinner from 'react-bootstrap/Spinner'
import Breadcrumb from 'react-bootstrap/Breadcrumb'
import { Sidebar } from './sidebar'

export default function SidebarLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading, isAuthenticated } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.replace('/login')
    }
  }, [loading, isAuthenticated, router])

  if (loading) {
    return (
      <div className="vh-100 d-flex align-items-center justify-content-center">
        <Spinner animation="border" variant="primary" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  const breadcrumbItems = getBreadcrumbItems(pathname)

  return (
    <div className="container-fluid p-0" style={{ height: '100vh' }}>
      <div className="row g-0 h-100">
        {/* Sidebar Column */}
        <div className="col-12 col-md-3 col-lg-2 bg-dark">
          <Sidebar />
        </div>

        {/* Main Content Column */}
        <div className="col-12 col-md-9 col-lg-10 d-flex flex-column">
          {/* Header */}
          <div className="bg-white border-bottom p-3">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <h4 className="mb-1">{getPageTitle(pathname)}</h4>
                <Breadcrumb className="mb-0">
                  {breadcrumbItems.map((item, index) => (
                    <Breadcrumb.Item key={index} active={index === breadcrumbItems.length - 1}>
                      {item.title}
                    </Breadcrumb.Item>
                  ))}
                </Breadcrumb>
              </div>

              <div className="text-muted small">
                {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-grow-1 p-0" style={{ overflow: 'auto' }}>
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

function getPageTitle(pathname: string): string {
  const titles: Record<string, string> = {
    '/dashboard': 'Dashboard',
    '/dashboard/users': 'Users Management',
    '/affiliates': 'Affiliates Management',
    '/organic-sales': 'Organic Sales Management',
    '/organic-orders': 'Organic Orders Management',
    '/order-infos': 'Order Infos Management',
    '/error-logs': 'Error Logs Management',
  }
  return titles[pathname] || 'GPC Everflow Manager'
}

function getBreadcrumbItems(pathname: string) {
  const items = [
    {
      title: 'Home',
    }
  ]

  const pathSegments = pathname.split('/').filter(Boolean)

  if (pathSegments.length > 0) {
    if (pathSegments[0] === 'dashboard') {
      items.push({ title: 'Dashboard' })
      if (pathSegments[1]) {
        items.push({ title: pathSegments[1].charAt(0).toUpperCase() + pathSegments[1].slice(1) })
      }
    } else {
      const title = pathSegments[0].split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ')
      items.push({ title })
    }
  }

  return items
}
