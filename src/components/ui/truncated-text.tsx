'use client'

import { useState } from 'react'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import Tooltip from 'react-bootstrap/Tooltip'

interface TruncatedTextProps {
  text: string
  maxLength?: number
  className?: string
  placement?: 'top' | 'bottom' | 'left' | 'right'
}

export function TruncatedText({ 
  text, 
  maxLength = 50, 
  className = '', 
  placement = 'top' 
}: TruncatedTextProps) {
  const [showTooltip, setShowTooltip] = useState(false)
  
  // If text is shorter than maxLength, just display it
  if (!text || text.length <= maxLength) {
    return <span className={className}>{text || '-'}</span>
  }

  const truncatedText = text.substring(0, maxLength) + '...'

  const renderTooltip = (props: any) => (
    <Tooltip id="truncated-tooltip" {...props} className="text-start">
      <div style={{ maxWidth: '400px', wordWrap: 'break-word' }}>
        {text}
      </div>
    </Tooltip>
  )

  return (
    <OverlayTrigger
      placement={placement}
      delay={{ show: 250, hide: 100 }}
      overlay={renderTooltip}
      show={showTooltip}
      onToggle={(nextShow) => setShowTooltip(nextShow)}
    >
      <span 
        className={`${className} text-truncate d-inline-block`}
        style={{ 
          maxWidth: '100%',
          cursor: 'help',
          textDecoration: 'underline dotted',
          textDecorationColor: '#6c757d'
        }}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {truncatedText}
      </span>
    </OverlayTrigger>
  )
}

// Variant for URLs with special handling
export function TruncatedUrl({
  url,
  maxLength = 70,
  className = '',
  placement = 'top'
}: Omit<TruncatedTextProps, 'text'> & { url: string }) {
  if (!url || url.length <= maxLength) {
    return (
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer" 
        className={`${className} text-primary text-decoration-none`}
      >
        {url || '-'}
      </a>
    )
  }

  const truncatedUrl = url.substring(0, maxLength) + '...'

  const renderTooltip = (props: any) => (
    <Tooltip id="url-tooltip" {...props} className="text-start">
      <div style={{ maxWidth: '500px', wordWrap: 'break-word' }}>
        <strong>Full URL:</strong><br />
        {url}
      </div>
    </Tooltip>
  )

  return (
    <OverlayTrigger
      placement={placement}
      delay={{ show: 250, hide: 100 }}
      overlay={renderTooltip}
    >
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer"
        className={`${className} text-primary text-decoration-none d-inline-block text-truncate`}
        style={{ 
          maxWidth: '100%',
          textDecoration: 'underline dotted',
          textDecorationColor: '#0d6efd'
        }}
        title="Click to open, hover for full URL"
      >
        {truncatedUrl}
      </a>
    </OverlayTrigger>
  )
}

// Variant for email addresses
export function TruncatedEmail({
  email,
  maxLength = 50,
  className = '',
  placement = 'top'
}: Omit<TruncatedTextProps, 'text'> & { email: string }) {
  if (!email || email.length <= maxLength) {
    return (
      <a 
        href={`mailto:${email}`} 
        className={`${className} text-primary text-decoration-none`}
      >
        {email || '-'}
      </a>
    )
  }

  const truncatedEmail = email.substring(0, maxLength) + '...'

  const renderTooltip = (props: any) => (
    <Tooltip id="email-tooltip" {...props}>
      <div style={{ maxWidth: '300px', wordWrap: 'break-word' }}>
        <strong>Email:</strong><br />
        {email}
      </div>
    </Tooltip>
  )

  return (
    <OverlayTrigger
      placement={placement}
      delay={{ show: 250, hide: 100 }}
      overlay={renderTooltip}
    >
      <a 
        href={`mailto:${email}`}
        className={`${className} text-primary text-decoration-none d-inline-block text-truncate`}
        style={{ 
          maxWidth: '100%',
          textDecoration: 'underline dotted',
          textDecorationColor: '#0d6efd'
        }}
        title="Click to send email, hover for full address"
      >
        {truncatedEmail}
      </a>
    </OverlayTrigger>
  )
}
