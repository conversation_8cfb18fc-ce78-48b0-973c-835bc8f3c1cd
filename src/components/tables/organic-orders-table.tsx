'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { TruncatedText, TruncatedUrl } from '@/components/ui/truncated-text'
import { OrganicOrderForm } from '@/components/forms/organic-order-form'
import { OverlayTrigger, Tooltip } from 'react-bootstrap'

interface OrganicOrder {
  _id: string
  affId: string
  offerId: string
  orderInfo: {
    orderNumber: string
    clickEvent: string
    conversionEvent: {
      transaction_id: string
      conversion_id: string
    }
    reffererUrl: string
    url: string
  }
  createdAt: string
  updatedAt: string
}

export function OrganicOrdersTable() {
  const [organicOrders, setOrganicOrders] = useState<OrganicOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingOrganicOrder, setEditingOrganicOrder] = useState<OrganicOrder | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const fetchOrganicOrders = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { affId: searchTerm }),
      })

      const response = await fetch(`/api/organic-orders?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch organic orders')
      }

      if (result.success) {
        setOrganicOrders(result.data || [])
        if (result.pagination) {
          setTotalPages(Math.ceil(result.pagination.totalRecords / 10))
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch organic orders')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrganicOrders()
  }, [currentPage, searchTerm])

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this organic order?')) {
      return
    }

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/organic-orders/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to delete organic order')
      }

      if (result.success) {
        fetchOrganicOrders()
      }
    } catch (error: any) {
      setError(error.message || 'Failed to delete organic order')
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingOrganicOrder(null)
    fetchOrganicOrders()
  }

  const handleEdit = (organicOrder: OrganicOrder) => {
    setEditingOrganicOrder(organicOrder)
    setShowForm(true)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const truncateUrl = (url: string, maxLength: number = 30) => {
    return url.length > maxLength ? `${url.substring(0, maxLength)}...` : url
  }

  if (showForm) {
    return (
      <OrganicOrderForm
        organicOrder={editingOrganicOrder}
        onSuccess={handleFormSuccess}
        onCancel={() => {
          setShowForm(false)
          setEditingOrganicOrder(null)
        }}
      />
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Organic Orders Management</CardTitle>
            <CardDescription>
              Manage organic orders and conversion tracking
            </CardDescription>
          </div>
          <Button onClick={() => setShowForm(true)}>
            Add New Organic Order
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <Input
            placeholder="Search by Affiliate ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        {error && (
          <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <Table className="w-full table-fixed"
                style={{ minWidth: '1200px' }}>
                <TableHeader>
                  <TableRow>
                    <TableHead style={{ width: '140px' }}>Order Number</TableHead>
                    <TableHead style={{ width: '120px' }}>Affiliate ID</TableHead>
                    <TableHead style={{ width: '100px' }}>Offer ID</TableHead>
                    <TableHead style={{ width: '160px' }}>Transaction ID</TableHead>
                    <TableHead style={{ width: '300px' }}>Landing URL</TableHead>
                    <TableHead style={{ width: '140px' }}>Created</TableHead>
                    <TableHead style={{ width: '140px' }}>Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {organicOrders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No organic orders found
                    </TableCell>
                  </TableRow>
                ) : (
                  organicOrders.map((organicOrder) => (
                    <TableRow key={organicOrder._id}>
                      <TableCell className="font-medium">
                        <TruncatedText text={organicOrder.orderInfo.orderNumber} maxLength={20} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={organicOrder.affId} maxLength={15} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={organicOrder.offerId} maxLength={12} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={organicOrder.orderInfo.conversionEvent.transaction_id} maxLength={20} />
                      </TableCell>
                      <TableCell>
                        <TruncatedUrl url={organicOrder.orderInfo.url} maxLength={50} />
                      </TableCell>
                      <TableCell>{formatDate(organicOrder.createdAt)}</TableCell>
                      <TableCell>
                        <div className="d-flex gap-1">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="edit-order-tooltip">Edit Order</Tooltip>}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(organicOrder)}
                              className="px-2 py-1"
                            >
                              <i className="bi bi-pencil"></i>
                            </Button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="delete-order-tooltip">Delete Order</Tooltip>}
                          >
                            <button
                              className="btn btn-sm btn-danger px-2 py-1"
                              onClick={() => handleDelete(organicOrder._id)}
                            >
                              <i className="bi bi-trash-fill"></i>
                            </button>
                          </OverlayTrigger>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
