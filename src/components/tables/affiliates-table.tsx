'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { AffiliateForm } from '@/components/forms/affiliate-form'
import { TruncatedText, TruncatedEmail } from '@/components/ui/truncated-text'
import { api } from '@/lib/api-client'
import { OverlayTrigger, Tooltip } from 'react-bootstrap'

interface AltAff {
  altAffId: string
  rate: number
  isPaused: boolean
}

interface Affiliate {
  _id: string
  affId: string
  offerId: string
  altAffId?: string
  altAffs?: AltAff[]
  rate: number
  orders: number
  total_not_fired: number
  startDate?: string
  endDate?: string
  userEmail: string
  isPaused: boolean
  createdAt: string
  updatedAt: string
}

export function AffiliatesTable() {
  const [affiliates, setAffiliates] = useState<Affiliate[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingAffiliate, setEditingAffiliate] = useState<Affiliate | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const fetchAffiliates = async () => {
    try {
      setLoading(true)

      const params: Record<string, string> = {
        page: currentPage.toString(),
        limit: '10',
      }

      if (searchTerm) {
        params.affId = searchTerm
      }

      const result = await api.get('/api/affiliates', params)

      if (result.success) {
        setAffiliates((result.data as Affiliate[]) || [])
        if (result.pagination) {
          setTotalPages(Math.ceil(result.pagination.totalRecords / 10))
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch affiliates')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAffiliates()
  }, [currentPage, searchTerm])

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this affiliate?')) {
      return
    }

    try {
      const result = await api.delete(`/api/affiliates/${id}`)

      if (result.success) {
        fetchAffiliates()
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to delete affiliate')
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingAffiliate(null)
    fetchAffiliates()
  }

  const handleEdit = (affiliate: Affiliate) => {
    setEditingAffiliate(affiliate)
    setShowForm(true)
  }

  const handleTogglePause = async (affiliate: Affiliate) => {
    try {
      const newPauseStatus = !affiliate.isPaused

      if (newPauseStatus) {
        // PAUSING: Update affiliate pause status AND pause all AltAffs
        const updateData = {
          isPaused: true,
          altAffs: affiliate.altAffs?.map(altAff => ({
            ...altAff, // Keep ALL existing data
            isPaused: true // Only change pause status
          })) || []
        }

        const result = await api.put(`/api/affiliates/${affiliate._id}`, updateData)
        if (result.success) {
          fetchAffiliates()
        }
      } else {
        // RESTORING: Only update affiliate pause status, don't touch AltAffs
        const updateData = {
          isPaused: false
          // Don't include altAffs - keep them exactly as they are
        }

        const result = await api.put(`/api/affiliates/${affiliate._id}`, updateData)
        if (result.success) {
          fetchAffiliates()
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update affiliate status')
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  if (showForm) {
    return (
      <AffiliateForm
        affiliate={editingAffiliate || undefined}
        onSuccess={handleFormSuccess}
        onCancel={() => {
          setShowForm(false)
          setEditingAffiliate(null)
        }}
      />
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl font-semibold text-gray-900">Affiliates Management</CardTitle>
            <CardDescription className="text-gray-600 mt-1">
              Manage affiliate partners and their offer assignments
            </CardDescription>
          </div>
          <Button
            onClick={() => setShowForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-sm"
          >
            <i className="bi bi-plus-circle me-2"></i>
            Add New Affiliate
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <Input
            placeholder="Search by Affiliate ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        {error && (
          <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <Table className="w-full table-fixed"
                style={{ minWidth: '1200px' }}>
                <TableHeader>
                  <TableRow>
                    <TableHead style={{ width: '120px' }}>Affiliate ID</TableHead>
                    <TableHead style={{ width: '100px' }}>Offer ID</TableHead>
                    <TableHead style={{ width: '200px' }}>Alt Aff ID</TableHead>
                    <TableHead style={{ width: '80px' }}>Rate</TableHead>
                    <TableHead style={{ width: '80px' }}>Orders</TableHead>
                    <TableHead style={{ width: '250px' }}>User Email</TableHead>
                    <TableHead style={{ width: '100px' }}>Status</TableHead>
                    <TableHead style={{ width: '140px' }}>Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {affiliates.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No affiliates found
                    </TableCell>
                  </TableRow>
                ) : (
                  affiliates.map((affiliate) => (
                    <TableRow
                      key={affiliate._id}
                      className={affiliate.isPaused ? 'bg-red-50' : ''}
                    >
                      <TableCell className="font-medium">
                        <TruncatedText text={affiliate.affId} maxLength={15} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={affiliate.offerId} maxLength={12} />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {affiliate.altAffId && (
                            <div className="text-sm">
                              <TruncatedText text={affiliate.altAffId} maxLength={25} />
                            </div>
                          )}
                          {affiliate.altAffs && affiliate.altAffs.length > 0 && (
                            <div className="space-y-1">
                              {affiliate.altAffs.slice(0, 2).map((altAff, index) => (
                                <div
                                  key={index}
                                  className={`text-xs p-1 rounded ${
                                    altAff.isPaused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                                  }`}
                                >
                                  <TruncatedText
                                    text={`${altAff.altAffId} (${altAff.rate}%) ${altAff.isPaused ? '- Paused' : ''}`}
                                    maxLength={30}
                                  />
                                </div>
                              ))}
                              {affiliate.altAffs.length > 2 && (
                                <div className="text-xs text-gray-500">
                                  +{affiliate.altAffs.length - 2} more
                                </div>
                              )}
                            </div>
                          )}
                          {!affiliate.altAffId && (!affiliate.altAffs || affiliate.altAffs.length === 0) && '-'}
                        </div>
                      </TableCell>
                      <TableCell className="text-right font-medium">{affiliate.rate?.toFixed(2) || '0.00'}%</TableCell>
                      <TableCell className="text-right">{affiliate.orders || 0}</TableCell>
                      <TableCell>
                        <TruncatedEmail email={affiliate.userEmail} maxLength={35} />
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          affiliate.isPaused
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {affiliate.isPaused ? 'Paused' : 'Active'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="d-flex gap-1">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="edit-tooltip">Edit Affiliate</Tooltip>}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(affiliate)}
                              className="px-2 py-1"
                            >
                              <i className="bi bi-pencil"></i>
                            </Button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="pause-tooltip">{affiliate.isPaused ? 'Restore' : 'Pause'} Affiliate</Tooltip>}
                          >
                            <button
                              className={`btn btn-sm px-2 py-1 ${affiliate.isPaused ? 'btn-success' : 'btn-warning'}`}
                              onClick={() => handleTogglePause(affiliate)}
                            >
                              <i className={`bi ${affiliate.isPaused ? 'bi-play-fill' : 'bi-pause-fill'}`}></i>
                            </button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="delete-tooltip">Delete Affiliate</Tooltip>}
                          >
                            <button
                              className="btn btn-sm btn-danger px-2 py-1"
                              onClick={() => handleDelete(affiliate._id)}
                            >
                              <i className="bi bi-trash-fill"></i>
                            </button>
                          </OverlayTrigger>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
