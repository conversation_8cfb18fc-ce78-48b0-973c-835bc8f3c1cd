'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { TruncatedText, TruncatedUrl } from '@/components/ui/truncated-text'
import { OrderInfoForm } from '@/components/forms/order-info-form'
import { OverlayTrigger, Tooltip } from 'react-bootstrap'

interface OrderInfo {
  _id: string
  orderNumber: string
  affId: string
  offerId: string
  amount: number
  currency?: string
  status?: 'pending' | 'confirmed' | 'cancelled' | 'refunded'
  customerInfo?: {
    email?: string
    firstName?: string
    lastName?: string
    phone?: string
  }
  createdAt: string
  updatedAt: string
}

export function OrderInfosTable() {
  const [orderInfos, setOrderInfos] = useState<OrderInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingOrderInfo, setEditingOrderInfo] = useState<OrderInfo | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const fetchOrderInfos = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { orderNumber: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
      })

      const response = await fetch(`/api/order-infos?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch order infos')
      }

      if (result.success) {
        setOrderInfos(result.data || [])
        if (result.pagination) {
          setTotalPages(Math.ceil(result.pagination.totalRecords / 10))
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch order infos')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrderInfos()
  }, [currentPage, searchTerm, statusFilter])

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this order info?')) {
      return
    }

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/order-infos/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to delete order info')
      }

      if (result.success) {
        fetchOrderInfos()
      }
    } catch (error: any) {
      setError(error.message || 'Failed to delete order info')
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingOrderInfo(null)
    fetchOrderInfos()
  }

  const handleEdit = (orderInfo: OrderInfo) => {
    setEditingOrderInfo(orderInfo)
    setShowForm(true)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatAmount = (amount: number, currency?: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount)
  }

  const getStatusBadge = (status?: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
      refunded: 'bg-gray-100 text-gray-800',
    }
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[(status || 'pending') as keyof typeof colors]}`}>
        {(status || 'pending').toUpperCase()}
      </span>
    )
  }

  if (showForm) {
    return (
      <OrderInfoForm
        orderInfo={editingOrderInfo}
        onSuccess={handleFormSuccess}
        onCancel={() => {
          setShowForm(false)
          setEditingOrderInfo(null)
        }}
      />
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Order Infos Management</CardTitle>
            <CardDescription>
              Manage order information and track order status
            </CardDescription>
          </div>
          <Button onClick={() => setShowForm(true)}>
            Add New Order
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <Input
            placeholder="Search by Order Number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="cancelled">Cancelled</option>
            <option value="refunded">Refunded</option>
          </select>
        </div>

        {error && (
          <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <Table className="w-full table-fixed"
                style={{ minWidth: '1100px' }}>
                <TableHeader>
                  <TableRow>
                    <TableHead style={{ width: '140px' }}>Order Number</TableHead>
                    <TableHead style={{ width: '120px' }}>Affiliate ID</TableHead>
                    <TableHead style={{ width: '100px' }}>Offer ID</TableHead>
                    <TableHead style={{ width: '120px' }}>Amount</TableHead>
                    <TableHead style={{ width: '100px' }}>Status</TableHead>
                    <TableHead style={{ width: '200px' }}>Customer</TableHead>
                    <TableHead style={{ width: '140px' }}>Created</TableHead>
                    <TableHead style={{ width: '140px' }}>Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {orderInfos.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      No order infos found
                    </TableCell>
                  </TableRow>
                ) : (
                  orderInfos.map((orderInfo) => (
                    <TableRow key={orderInfo._id}>
                      <TableCell className="font-medium">
                        <TruncatedText text={orderInfo.orderNumber} maxLength={20} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={orderInfo.affId} maxLength={15} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={orderInfo.offerId} maxLength={12} />
                      </TableCell>
                      <TableCell>{formatAmount(orderInfo.amount, orderInfo.currency)}</TableCell>
                      <TableCell>{getStatusBadge(orderInfo.status)}</TableCell>
                      <TableCell>
                        <TruncatedText
                          text={orderInfo.customerInfo?.email ||
                               `${orderInfo.customerInfo?.firstName || ''} ${orderInfo.customerInfo?.lastName || ''}`.trim() ||
                               'N/A'}
                          maxLength={30}
                        />
                      </TableCell>
                      <TableCell>{formatDate(orderInfo.createdAt)}</TableCell>
                      <TableCell>
                        <div className="d-flex gap-1">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="edit-orderinfo-tooltip">Edit Order Info</Tooltip>}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(orderInfo)}
                              className="px-2 py-1"
                            >
                              <i className="bi bi-pencil"></i>
                            </Button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="delete-orderinfo-tooltip">Delete Order Info</Tooltip>}
                          >
                            <button
                              className="btn btn-sm btn-danger px-2 py-1"
                              onClick={() => handleDelete(orderInfo._id)}
                            >
                              <i className="bi bi-trash-fill"></i>
                            </button>
                          </OverlayTrigger>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
