'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { TruncatedText } from '@/components/ui/truncated-text'
import { OverlayTrigger, Tooltip } from 'react-bootstrap'

interface ErrorLog {
  _id: string
  level: 'error' | 'warn' | 'info' | 'debug'
  message: string
  stack?: string
  url?: string
  method?: string
  userAgent?: string
  ip?: string
  userId?: string
  timestamp: string
  isResolved: boolean
  resolvedBy?: string
  resolvedAt?: string
  createdAt: string
}

export function ErrorLogsTable() {
  const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState('')
  const [resolvedFilter, setResolvedFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const fetchErrorLogs = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(levelFilter && { level: levelFilter }),
        ...(resolvedFilter && { isResolved: resolvedFilter }),
      })

      const response = await fetch(`/api/error-logs?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch error logs')
      }

      if (result.success) {
        setErrorLogs(result.data || [])
        if (result.pagination) {
          setTotalPages(Math.ceil(result.pagination.totalRecords / 10))
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch error logs')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchErrorLogs()
  }, [currentPage, levelFilter, resolvedFilter])

  const handleResolve = async (id: string) => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/error-logs/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          isResolved: true,
          resolvedBy: 'current-user', // In real app, get from auth context
          resolvedAt: new Date().toISOString(),
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to resolve error log')
      }

      if (result.success) {
        fetchErrorLogs()
      }
    } catch (error: any) {
      setError(error.message || 'Failed to resolve error log')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this error log?')) {
      return
    }

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/error-logs/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to delete error log')
      }

      if (result.success) {
        fetchErrorLogs()
      }
    } catch (error: any) {
      setError(error.message || 'Failed to delete error log')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getLevelBadge = (level: string) => {
    const colors = {
      error: 'bg-red-100 text-red-800',
      warn: 'bg-yellow-100 text-yellow-800',
      info: 'bg-blue-100 text-blue-800',
      debug: 'bg-gray-100 text-gray-800',
    }
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[level as keyof typeof colors]}`}>
        {level.toUpperCase()}
      </span>
    )
  }

  const truncateMessage = (message: string, maxLength: number = 100) => {
    return message.length > maxLength ? `${message.substring(0, maxLength)}...` : message
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Error Logs Management</CardTitle>
            <CardDescription>
              Monitor and manage application error logs
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <select
            value={levelFilter}
            onChange={(e) => setLevelFilter(e.target.value)}
            className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
          >
            <option value="">All Levels</option>
            <option value="error">Error</option>
            <option value="warn">Warning</option>
            <option value="info">Info</option>
            <option value="debug">Debug</option>
          </select>

          <select
            value={resolvedFilter}
            onChange={(e) => setResolvedFilter(e.target.value)}
            className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
          >
            <option value="">All Status</option>
            <option value="false">Unresolved</option>
            <option value="true">Resolved</option>
          </select>
        </div>

        {error && (
          <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <Table className="w-full table-fixed"
                style={{ minWidth: '1200px' }}>
                <TableHeader>
                  <TableRow>
                    <TableHead style={{ width: '100px' }}>Level</TableHead>
                    <TableHead style={{ width: '300px' }}>Message</TableHead>
                    <TableHead style={{ width: '250px' }}>URL</TableHead>
                    <TableHead style={{ width: '180px' }}>Timestamp</TableHead>
                    <TableHead style={{ width: '120px' }}>Status</TableHead>
                    <TableHead style={{ width: '140px' }}>Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {errorLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No error logs found
                    </TableCell>
                  </TableRow>
                ) : (
                  errorLogs.map((errorLog) => (
                    <TableRow key={errorLog._id}>
                      <TableCell>{getLevelBadge(errorLog.level)}</TableCell>
                      <TableCell className="font-medium">
                        <TruncatedText text={errorLog.message} maxLength={50} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={errorLog.url || 'N/A'} maxLength={40} />
                      </TableCell>
                      <TableCell>{formatDate(errorLog.timestamp)}</TableCell>
                      <TableCell>
                        {errorLog.isResolved ? (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Resolved
                          </span>
                        ) : (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Unresolved
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="d-flex gap-1">
                          {!errorLog.isResolved && (
                            <OverlayTrigger
                              placement="top"
                              overlay={<Tooltip id="resolve-log-tooltip">Mark as Resolved</Tooltip>}
                            >
                              <button
                                className="btn btn-sm btn-success px-2 py-1"
                                onClick={() => handleResolve(errorLog._id)}
                              >
                                <i className="bi bi-check-circle"></i>
                              </button>
                            </OverlayTrigger>
                          )}

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="delete-log-tooltip">Delete Log</Tooltip>}
                          >
                            <button
                              className="btn btn-sm btn-danger px-2 py-1"
                              onClick={() => handleDelete(errorLog._id)}
                            >
                              <i className="bi bi-trash-fill"></i>
                            </button>
                          </OverlayTrigger>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
