'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { TruncatedText, TruncatedUrl } from '@/components/ui/truncated-text'
import { api } from '@/lib/api-client'
import { OverlayTrigger, Tooltip } from 'react-bootstrap'

interface OrganicSale {
  _id: string
  pageURL: string
  affId: string
  offerId: string
  amount: number
  currency: string
  status: 'pending' | 'confirmed' | 'cancelled' | 'refunded'
  customerInfo: {
    email: string
    firstName: string
    lastName: string
    phone?: string
  }
  orderInfo: {
    orderNumber: string
    clickEvent: string
    conversionEvent: {
      eventType: string
      timestamp: string
    }
  }
  isPaused: boolean
  createdAt: string
  updatedAt: string
}

export function OrganicSalesTable() {
  const [organicSales, setOrganicSales] = useState<OrganicSale[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const fetchOrganicSales = async () => {
    try {
      setLoading(true)

      const params: Record<string, string> = {
        page: currentPage.toString(),
        limit: '10',
      }

      if (searchTerm) {
        params.pageURL = searchTerm
      }

      const result = await api.get('/api/organic-sales', params)

      if (result.success) {
        setOrganicSales((result.data as OrganicSale[]) || [])
        if (result.pagination) {
          setTotalPages(Math.ceil(result.pagination.totalRecords / 10))
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch organic sales')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrganicSales()
  }, [currentPage, searchTerm])

  const handleTogglePause = async (organicSale: OrganicSale) => {
    try {
      const result = await api.put(`/api/organic-sales/${organicSale._id}`, {
        isPaused: !organicSale.isPaused
      })
      if (result.success) {
        fetchOrganicSales()
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update organic sale status')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this organic sale?')) {
      return
    }

    try {
      const result = await api.delete(`/api/organic-sales/${id}`)

      if (result.success) {
        fetchOrganicSales()
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to delete organic sale')
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Organic Sales Management</CardTitle>
            <CardDescription>
              Manage organic sales and their status
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <Input
            placeholder="Search by Page URL..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        {error && (
          <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <Table className="w-full table-fixed"
                style={{ minWidth: '1400px' }}>
                <TableHeader>
                  <TableRow>
                    <TableHead style={{ width: '300px' }}>Page URL</TableHead>
                    <TableHead style={{ width: '120px' }}>Affiliate ID</TableHead>
                    <TableHead style={{ width: '100px' }}>Offer ID</TableHead>
                    <TableHead style={{ width: '120px' }}>Amount</TableHead>
                    <TableHead style={{ width: '100px' }}>Status</TableHead>
                    <TableHead style={{ width: '200px' }}>Customer</TableHead>
                    <TableHead style={{ width: '120px' }}>Order #</TableHead>
                    <TableHead style={{ width: '100px' }}>State</TableHead>
                    <TableHead style={{ width: '140px' }}>Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {organicSales.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      No organic sales found
                    </TableCell>
                  </TableRow>
                ) : (
                  organicSales.map((sale) => (
                    <TableRow
                      key={sale._id}
                      className={sale.isPaused ? 'bg-red-50' : ''}
                    >
                      <TableCell className="font-medium">
                        <TruncatedUrl url={sale.pageURL} maxLength={50} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={sale.affId} maxLength={15} />
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={sale.offerId} maxLength={12} />
                      </TableCell>
                      <TableCell className="text-right font-medium">{sale.currency} ${sale.amount?.toFixed(2) || '0.00'}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          sale.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                          sale.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          sale.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {sale.status?.toUpperCase() || 'UNKNOWN'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>
                            <TruncatedText
                              text={`${sale.customerInfo?.firstName || ''} ${sale.customerInfo?.lastName || ''}`.trim() || 'N/A'}
                              maxLength={25}
                            />
                          </div>
                          <div className="text-gray-500">
                            <TruncatedText text={sale.customerInfo?.email || 'N/A'} maxLength={30} />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <TruncatedText text={sale.orderInfo?.orderNumber || 'N/A'} maxLength={15} />
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          sale.isPaused
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {sale.isPaused ? 'Paused' : 'Active'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="d-flex gap-1">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="pause-sale-tooltip">{sale.isPaused ? 'Restore' : 'Pause'} Sale</Tooltip>}
                          >
                            <button
                              className={`btn btn-sm px-2 py-1 ${sale.isPaused ? 'btn-success' : 'btn-warning'}`}
                              onClick={() => handleTogglePause(sale)}
                            >
                              <i className={`bi ${sale.isPaused ? 'bi-play-fill' : 'bi-pause-fill'}`}></i>
                            </button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="delete-sale-tooltip">Delete Sale</Tooltip>}
                          >
                            <button
                              className="btn btn-sm btn-danger px-2 py-1"
                              onClick={() => handleDelete(sale._id)}
                            >
                              <i className="bi bi-trash-fill"></i>
                            </button>
                          </OverlayTrigger>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
