'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { TruncatedText, TruncatedEmail } from '@/components/ui/truncated-text'
import { UserEditForm } from '@/components/forms/user-edit-form'
import { OverlayTrigger, Tooltip } from 'react-bootstrap'

interface User {
  _id: string
  email: string
  firstName: string
  lastName: string
  role: string
  createdAt: string
  updatedAt: string
}

export function UsersTable() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [editingUser, setEditingUser] = useState<User | null>(null)

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { email: searchTerm }),
      })

      const response = await fetch(`/api/users?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch users')
      }

      if (result.success) {
        setUsers(result.data || [])
        if (result.pagination) {
          setTotalPages(Math.ceil(result.pagination.totalRecords / 10))
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [currentPage, searchTerm])

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this user?')) {
      return
    }

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/users/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to delete user')
      }

      if (result.success) {
        fetchUsers()
      }
    } catch (error: any) {
      setError(error.message || 'Failed to delete user')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getRoleBadge = (role: string) => {
    const colors = {
      admin: 'badge bg-danger',
      manager: 'badge bg-primary',
      user: 'badge bg-success',
    }
    return (
      <span className={colors[role as keyof typeof colors] || 'badge bg-secondary'}>
        {role.toUpperCase()}
      </span>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Users Management</CardTitle>
            <CardDescription>
              Manage user accounts and permissions
            </CardDescription>
          </div>
          <Button onClick={() => window.open('/register', '_blank')}>
            Add New User
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <Input
            placeholder="Search by email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        {error && (
          <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <Table className="w-full table-fixed"
                style={{ minWidth: '900px' }}>
                <TableHeader>
                  <TableRow>
                    <TableHead style={{ width: '200px' }}>Name</TableHead>
                    <TableHead style={{ width: '300px' }}>Email</TableHead>
                    <TableHead style={{ width: '120px' }}>Role</TableHead>
                    <TableHead style={{ width: '140px' }}>Created</TableHead>
                    <TableHead style={{ width: '140px' }}>Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user._id}>
                      <TableCell className="font-medium">
                        <TruncatedText text={`${user.firstName} ${user.lastName}`} maxLength={25} />
                      </TableCell>
                      <TableCell>
                        <TruncatedEmail email={user.email} maxLength={40} />
                      </TableCell>
                      <TableCell>{getRoleBadge(user.role)}</TableCell>
                      <TableCell className="text-sm">{formatDate(user.createdAt)}</TableCell>
                      <TableCell>
                        <div className="d-flex gap-1">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="edit-user-tooltip">Edit User</Tooltip>}
                          >
                            <button
                              className="btn btn-sm btn-outline-primary px-2 py-1"
                              onClick={() => setEditingUser(user)}
                            >
                              <i className="bi bi-pencil"></i>
                            </button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip id="delete-user-tooltip">Delete User</Tooltip>}
                          >
                            <button
                              className="btn btn-sm btn-danger px-2 py-1"
                              onClick={() => handleDelete(user._id)}
                            >
                              <i className="bi bi-trash-fill"></i>
                            </button>
                          </OverlayTrigger>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>

      {/* Edit User Modal */}
      {editingUser && (
        <div className="modal fade show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <UserEditForm
                user={editingUser}
                onSuccess={() => {
                  setEditingUser(null)
                  fetchUsers()
                }}
                onCancel={() => setEditingUser(null)}
              />
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}
