import { z } from 'zod'

// User schemas
export const userSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.string().optional().default('user'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
})

export const userUpdateSchema = z.object({
  email: z.string().email('Invalid email address').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  role: z.string().optional(),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
})

export const signInSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
})

export const changePasswordSchema = z.object({
  oldPassword: z.string().min(1, 'Old password is required'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
})

// AltAff item schema
export const altAffSchema = z.object({
  altAffId: z.string().min(1, 'Alt Aff ID is required'),
  rate: z.number().min(0, 'Rate must be non-negative'),
  isPaused: z.boolean().default(false),
})

// Affiliate schemas
export const affiliateSchema = z.object({
  affId: z.string().min(1, 'Affiliate ID is required'),
  offerId: z.string().min(1, 'Offer ID is required'),
  altAffId: z.string().optional(),
  altAffs: z.array(altAffSchema).optional().default([]),
  rate: z.number().min(0, 'Rate must be non-negative').default(0),
  orders: z.number().min(0, 'Orders must be non-negative').default(0),
  total_not_fired: z.number().min(0, 'Total not fired must be non-negative').default(0),
  startDate: z.string().or(z.date()).optional(),
  endDate: z.string().or(z.date()).optional(),
  userEmail: z.string().email('Invalid email address'),
  isPaused: z.boolean().default(false),
}).refine((data) => {
  // If altAffs is empty, altAffId is required
  if (!data.altAffs || data.altAffs.length === 0) {
    return data.altAffId && data.altAffId.trim().length > 0
  }
  return true
}, {
  message: 'Either altAffId or at least one item in altAffs is required',
  path: ['altAffId']
})

export const affiliateUpdateSchema = z.object({
  altAffId: z.string().optional(),
  altAffs: z.array(altAffSchema).optional(),
  rate: z.number().min(0, 'Rate must be non-negative').optional(),
  orders: z.number().min(0, 'Orders must be non-negative').optional(),
  total_not_fired: z.number().min(0, 'Total not fired must be non-negative').optional(),
  startDate: z.string().or(z.date()).optional(),
  endDate: z.string().or(z.date()).optional(),
  userEmail: z.string().email('Invalid email address').optional(),
  isPaused: z.boolean().optional(),
})

// Organic Sale schemas
export const organicSaleSchema = z.object({
  pageURL: z.string().url('Invalid URL format'),
  affId: z.string().min(1, 'Affiliate ID is required').optional(),
  offerId: z.string().min(1, 'Offer ID is required').optional(),
})

// Alternate Affiliate schemas
export const alternateAffiliateSchema = z.object({
  altAffId: z.string().min(1, 'Alternate Affiliate ID is required'),
  rate: z.number().min(0, 'Rate must be non-negative').default(0),
  totalOrdersFired: z.number().min(0, 'Total orders fired must be non-negative').default(0),
  startDate: z.string().or(z.date()),
  endDate: z.string().or(z.date()),
  isPaused: z.boolean().default(false),
})

// Organic Order schemas
export const organicOrderSchema = z.object({
  affId: z.string().min(1, 'Affiliate ID is required'),
  offerId: z.string().min(1, 'Offer ID is required'),
  orderInfo: z.object({
    orderNumber: z.string().min(1, 'Order number is required'),
    clickEvent: z.string().min(1, 'Click event is required'),
    conversionEvent: z.object({
      transaction_id: z.string().min(1, 'Transaction ID is required'),
      conversion_id: z.string().min(1, 'Conversion ID is required'),
    }),
    reffererUrl: z.string().url('Invalid referrer URL'),
    url: z.string().url('Invalid URL'),
  }),
})

// Error Log schemas
export const errorLogSchema = z.object({
  level: z.enum(['error', 'warn', 'info', 'debug']).default('error'),
  message: z.string().min(1, 'Message is required'),
  stack: z.string().optional(),
  url: z.string().optional(),
  method: z.string().optional(),
  userAgent: z.string().optional(),
  ip: z.string().optional(),
  userId: z.string().optional(),
  timestamp: z.string().or(z.date()).optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

export const errorLogUpdateSchema = z.object({
  isResolved: z.boolean().optional(),
  resolvedBy: z.string().optional(),
  resolvedAt: z.string().or(z.date()).optional(),
})

// Order Info schemas
export const orderInfoSchema = z.object({
  orderNumber: z.string().min(1, 'Order number is required'),
  affId: z.string().min(1, 'Affiliate ID is required'),
  offerId: z.string().min(1, 'Offer ID is required'),
  amount: z.number().min(0, 'Amount must be non-negative'),
  currency: z.string().min(1, 'Currency is required').optional().default('USD'),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'refunded']).optional().default('pending'),
  customerInfo: z.object({
    email: z.string().email('Invalid email').optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

export const orderInfoFormSchema = z.object({
  orderNumber: z.string().min(1, 'Order number is required'),
  affId: z.string().min(1, 'Affiliate ID is required'),
  offerId: z.string().min(1, 'Offer ID is required'),
  amount: z.number().min(0, 'Amount must be non-negative'),
  currency: z.string().min(1, 'Currency is required').optional().transform(val => val || 'USD'),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'refunded']).optional().transform(val => val || 'pending'),
  customerInfo: z.object({
    email: z.string().email('Invalid email').optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

// API Response schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  message: z.string().optional(),
  pagination: z.object({
    currentPage: z.number(),
    pageSize: z.number(),
    totalRecords: z.number(),
  }).optional(),
})

// Query parameter schemas
export const paginationSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
})

export const userQuerySchema = paginationSchema.extend({
  email: z.string().optional(),
})

export const affiliateQuerySchema = paginationSchema.extend({
  affId: z.string().optional(),
  offerId: z.string().optional(),
})

export const errorLogQuerySchema = paginationSchema.extend({
  level: z.enum(['error', 'warn', 'info', 'debug']).optional(),
  isResolved: z.string().optional().transform(val => val === 'true'),
  userId: z.string().optional(),
})

export const orderInfoQuerySchema = paginationSchema.extend({
  orderNumber: z.string().optional(),
  affId: z.string().optional(),
  offerId: z.string().optional(),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'refunded']).optional(),
})

// Type exports
export type UserInput = z.infer<typeof userSchema>
export type UserUpdateInput = z.infer<typeof userUpdateSchema>
export type SignInInput = z.infer<typeof signInSchema>
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>
export type AltAffInput = z.infer<typeof altAffSchema>
export type AffiliateInput = z.infer<typeof affiliateSchema>
export type AffiliateUpdateInput = z.infer<typeof affiliateUpdateSchema>
export type OrganicSaleInput = z.infer<typeof organicSaleSchema>
export type AlternateAffiliateInput = z.infer<typeof alternateAffiliateSchema>
export type OrganicOrderInput = z.infer<typeof organicOrderSchema>
export type ErrorLogInput = z.infer<typeof errorLogSchema>
export type ErrorLogUpdateInput = z.infer<typeof errorLogUpdateSchema>
export type OrderInfoInput = z.infer<typeof orderInfoSchema>
export type OrderInfoFormInput = z.infer<typeof orderInfoFormSchema>
export type ApiResponse = z.infer<typeof apiResponseSchema>
export type PaginationQuery = z.infer<typeof paginationSchema>
export type UserQuery = z.infer<typeof userQuerySchema>
export type AffiliateQuery = z.infer<typeof affiliateQuerySchema>
export type ErrorLogQuery = z.infer<typeof errorLogQuerySchema>
export type OrderInfoQuery = z.infer<typeof orderInfoQuerySchema>
