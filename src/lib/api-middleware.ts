import { NextRequest, NextResponse } from 'next/server'

export function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': process.env.NODE_ENV === 'production' 
      ? process.env.ALLOWED_ORIGINS || 'https://yourdomain.com'
      : '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400', // 24 hours
  }
}

export function securityHeaders() {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;",
  }
}

export function handleCors(request: NextRequest) {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 200,
      headers: {
        ...corsHeaders(),
        ...securityHeaders(),
      },
    })
  }
  return null
}

export function withApiMiddleware(handler: (request: NextRequest, context?: any) => Promise<Response>) {
  return async (request: NextRequest, context?: any) => {
    // Handle CORS preflight
    const corsResponse = handleCors(request)
    if (corsResponse) return corsResponse

    try {
      // Call the actual handler
      const response = await handler(request, context)
      
      // Add security and CORS headers to the response
      const headers = {
        ...corsHeaders(),
        ...securityHeaders(),
      }
      
      Object.entries(headers).forEach(([key, value]) => {
        response.headers.set(key, value)
      })
      
      return response
    } catch (error: any) {
      console.error('API Error:', error)
      
      return NextResponse.json(
        {
          success: false,
          message: process.env.NODE_ENV === 'production' 
            ? 'Internal server error' 
            : error.message,
        },
        {
          status: 500,
          headers: {
            ...corsHeaders(),
            ...securityHeaders(),
          },
        }
      )
    }
  }
}

// Request validation middleware
export function validateRequestMethod(allowedMethods: string[]) {
  return (request: NextRequest) => {
    if (!allowedMethods.includes(request.method)) {
      return NextResponse.json(
        {
          success: false,
          message: `Method ${request.method} not allowed`,
        },
        {
          status: 405,
          headers: {
            Allow: allowedMethods.join(', '),
            ...corsHeaders(),
            ...securityHeaders(),
          },
        }
      )
    }
    return null
  }
}

// Content-Type validation
export function validateContentType(request: NextRequest, requiredType: string = 'application/json') {
  if (['POST', 'PUT', 'PATCH'].includes(request.method)) {
    const contentType = request.headers.get('content-type')
    if (!contentType || !contentType.includes(requiredType)) {
      return NextResponse.json(
        {
          success: false,
          message: `Content-Type must be ${requiredType}`,
        },
        {
          status: 400,
          headers: {
            ...corsHeaders(),
            ...securityHeaders(),
          },
        }
      )
    }
  }
  return null
}

// Request size validation
export function validateRequestSize(request: NextRequest, maxSize: number = 1024 * 1024) { // 1MB default
  const contentLength = request.headers.get('content-length')
  if (contentLength && parseInt(contentLength) > maxSize) {
    return NextResponse.json(
      {
        success: false,
        message: 'Request body too large',
      },
      {
        status: 413,
        headers: {
          ...corsHeaders(),
          ...securityHeaders(),
        },
      }
    )
  }
  return null
}
