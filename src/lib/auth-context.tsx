'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  _id: string
  email: string
  firstName: string
  lastName: string
  role: string
  tokenLogin: string
}

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  loading: boolean
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  const isAuthenticated = !!user

  useEffect(() => {
    // Check if user is logged in on mount
    const token = localStorage.getItem('token')
    const userInfo = localStorage.getItem('user')

    console.log('Auth Context - Token:', !!token, 'UserInfo:', !!userInfo)

    if (token && userInfo) {
      try {
        const parsedUser = JSON.parse(userInfo)
        console.log('Auth Context - Setting user:', parsedUser)
        setUser(parsedUser)

        // Verify token is still valid
        verifyTokenValidity(token)
      } catch (error) {
        console.log('Auth Context - Error parsing user info:', error)
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      }
    }

    setLoading(false)
  }, [])

  // Function to verify token validity
  const verifyTokenValidity = async (token: string) => {
    try {
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        console.log('Auth Context - Token invalid, logging out')
        await logout()
      }
    } catch (error) {
      console.log('Auth Context - Error verifying token:', error)
      await logout()
    }
  }

  const login = async (email: string, password: string) => {
    try {
      console.log('Auth Context - Starting login for:', email)
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      console.log('Auth Context - Response status:', response.status)
      const data = await response.json()
      console.log('Auth Context - Response data:', data)

      if (!response.ok) {
        throw new Error(data.message || 'Login failed')
      }

      if (data.success) {
        console.log('Auth Context - Login successful, setting user data')
        localStorage.setItem('token', data.data.tokenLogin)
        localStorage.setItem('user', JSON.stringify(data.data))
        setUser(data.data)
        console.log('Auth Context - Redirecting to dashboard')
        router.replace('/dashboard')
      } else {
        throw new Error(data.message || 'Login failed')
      }
    } catch (error) {
      console.log('Auth Context - Login error:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      const token = localStorage.getItem('token')

      if (token) {
        await fetch('/api/auth/signout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      setUser(null)
      router.replace('/login')
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      loading,
      isAuthenticated,
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
