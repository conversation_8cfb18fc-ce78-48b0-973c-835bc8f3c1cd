import { NextResponse } from 'next/server'
import { ZodError } from 'zod'
import { logger } from './logger'

export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational

    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400)
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401)
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403)
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404)
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource already exists') {
    super(message, 409)
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429)
  }
}

export function handleApiError(error: unknown, request?: Request): NextResponse {
  let statusCode = 500
  let message = 'Internal server error'
  let details: any = undefined

  // Log the error
  if (error instanceof Error) {
    logger.error('API Error', error, {
      url: request?.url,
      method: request?.method,
    })
  }

  if (error instanceof AppError) {
    statusCode = error.statusCode
    message = error.message
  } else if (error instanceof ZodError) {
    statusCode = 400
    message = 'Validation error'
    details = error.issues.map(err => ({
      field: err.path.join('.'),
      message: err.message,
    }))
  } else if (error instanceof Error) {
    // MongoDB errors
    if (error.name === 'ValidationError') {
      statusCode = 400
      message = 'Database validation error'
    } else if (error.name === 'CastError') {
      statusCode = 400
      message = 'Invalid ID format'
    } else if (error.name === 'MongoServerError' && (error as any).code === 11000) {
      statusCode = 409
      message = 'Resource already exists'
    } else if (error.name === 'JsonWebTokenError') {
      statusCode = 401
      message = 'Invalid token'
    } else if (error.name === 'TokenExpiredError') {
      statusCode = 401
      message = 'Token expired'
    }
    
    // In development, include the actual error message
    if (process.env.NODE_ENV === 'development') {
      message = error.message
    }
  }

  const response = {
    success: false,
    message,
    ...(details && { details }),
    ...(process.env.NODE_ENV === 'development' && error instanceof Error && {
      stack: error.stack,
    }),
  }

  return NextResponse.json(response, { status: statusCode })
}

// Async error wrapper for API routes
export function asyncHandler(
  handler: (request: Request, context?: any) => Promise<Response>
) {
  return async (request: Request, context?: any): Promise<Response> => {
    try {
      return await handler(request, context)
    } catch (error) {
      return handleApiError(error, request)
    }
  }
}

// Database error handler
export function handleDatabaseError(error: any): never {
  if (error.name === 'ValidationError') {
    const messages = Object.values(error.errors).map((err: any) => err.message)
    throw new ValidationError(messages.join(', '))
  }
  
  if (error.name === 'CastError') {
    throw new ValidationError('Invalid ID format')
  }
  
  if (error.name === 'MongoServerError' && error.code === 11000) {
    const field = Object.keys(error.keyPattern)[0]
    throw new ConflictError(`${field} already exists`)
  }
  
  // Log unexpected database errors
  logger.error('Unexpected database error', error)
  throw new AppError('Database operation failed')
}

// Validation error helper
export function createValidationError(field: string, message: string): ValidationError {
  return new ValidationError(`${field}: ${message}`)
}

// Success response helper
export function createSuccessResponse(
  data?: any,
  message?: string,
  statusCode: number = 200
): NextResponse {
  return NextResponse.json({
    success: true,
    ...(data !== undefined && { data }),
    ...(message && { message }),
  }, { status: statusCode })
}

// Error response helper
export function createErrorResponse(
  message: string,
  statusCode: number = 500,
  details?: any
): NextResponse {
  return NextResponse.json({
    success: false,
    message,
    ...(details && { details }),
  }, { status: statusCode })
}
