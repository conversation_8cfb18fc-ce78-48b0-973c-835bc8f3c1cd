import { NextRequest } from 'next/server'

interface RateLimitStore {
  [key: string]: {
    count: number
    resetTime: number
  }
}

class RateLimiter {
  private store: RateLimitStore = {}
  private windowMs: number
  public maxRequests: number

  constructor(windowMs: number = 15 * 60 * 1000, maxRequests: number = 100) {
    this.windowMs = windowMs
    this.maxRequests = maxRequests
  }

  private getKey(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded ? forwarded.split(',')[0] : realIp || 'unknown'
    return ip
  }

  private cleanupExpired(): void {
    const now = Date.now()
    Object.keys(this.store).forEach(key => {
      if (this.store[key].resetTime < now) {
        delete this.store[key]
      }
    })
  }

  check(request: NextRequest): { allowed: boolean; remaining: number; resetTime: number } {
    this.cleanupExpired()
    
    const key = this.getKey(request)
    const now = Date.now()
    
    if (!this.store[key] || this.store[key].resetTime < now) {
      this.store[key] = {
        count: 1,
        resetTime: now + this.windowMs
      }
      return {
        allowed: true,
        remaining: this.maxRequests - 1,
        resetTime: this.store[key].resetTime
      }
    }

    this.store[key].count++
    
    return {
      allowed: this.store[key].count <= this.maxRequests,
      remaining: Math.max(0, this.maxRequests - this.store[key].count),
      resetTime: this.store[key].resetTime
    }
  }
}

// Create different rate limiters for different endpoints
export const generalLimiter = new RateLimiter(15 * 60 * 1000, 100) // 100 requests per 15 minutes
export const authLimiter = new RateLimiter(15 * 60 * 1000, 5) // 5 login attempts per 15 minutes
export const apiLimiter = new RateLimiter(60 * 1000, 60) // 60 requests per minute for API

export function applyRateLimit(request: NextRequest, limiter: RateLimiter) {
  const result = limiter.check(request)
  
  if (!result.allowed) {
    return Response.json(
      {
        success: false,
        message: 'Too many requests. Please try again later.',
        retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
      },
      { 
        status: 429,
        headers: {
          'X-RateLimit-Limit': limiter.maxRequests.toString(),
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': result.resetTime.toString(),
          'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString()
        }
      }
    )
  }

  return null // No rate limit exceeded
}
