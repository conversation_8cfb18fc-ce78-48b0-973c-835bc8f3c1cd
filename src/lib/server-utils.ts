import 'server-only'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'

// Password utilities (server-only)
export const hashPassword = async (password: string): Promise<string> => {
  return bcrypt.hash(password, 10)
}

export const comparePassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword)
}

// JWT utilities (server-only)
const JWT_SECRET = process.env.JWT_SECRET || process.env.JWT_SECRET_KEY || 'DFO GPC'

export interface TokenPayload {
  firstName: string
  lastName: string
  email: string
  role: string
}

export const encodeToken = (payload: TokenPayload): string => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })
}

export const verifyToken = (token: string): TokenPayload => {
  try {
    return jwt.verify(token, JWT_SECRET) as TokenPayload
  } catch {
    throw new Error('Invalid token')
  }
}

export const decodeToken = (token: string): TokenPayload | null => {
  try {
    return jwt.decode(token) as TokenPayload
  } catch {
    return null
  }
}

// API Response utilities
export const createApiResponse = (
  success: boolean,
  data?: unknown,
  message?: string,
  pagination?: {
    currentPage: number
    pageSize: number
    totalRecords: number
  }
) => {
  return {
    success,
    ...(data !== undefined && { data }),
    ...(message && { message }),
    ...(pagination && { pagination }),
  }
}

export const createErrorResponse = (message: string, statusCode: number = 500) => {
  return Response.json(
    createApiResponse(false, null, message),
    { status: statusCode }
  )
}

export const createSuccessResponse = (
  data?: unknown,
  message?: string,
  pagination?: {
    currentPage: number
    pageSize: number
    totalRecords: number
  }
) => {
  return Response.json(
    createApiResponse(true, data, message, pagination),
    { status: 200 }
  )
}

// Date utilities
export const formatDate = (date: Date | string): string => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

export const formatDateTime = (date: Date | string): string => {
  return new Date(date).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Pagination utilities
export const getPaginationParams = (searchParams: URLSearchParams) => {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
  const limit = Math.max(1, Math.min(100, parseInt(searchParams.get('limit') || '10')))
  const skip = (page - 1) * limit

  return { page, limit, skip }
}

export const createPaginationInfo = (
  currentPage: number,
  pageSize: number,
  totalRecords: number
) => {
  return {
    currentPage,
    pageSize,
    totalRecords,
    totalPages: Math.ceil(totalRecords / pageSize),
    hasNextPage: currentPage * pageSize < totalRecords,
    hasPrevPage: currentPage > 1,
  }
}
