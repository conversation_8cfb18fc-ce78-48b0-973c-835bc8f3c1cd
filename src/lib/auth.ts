import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { User } from '@/models'
import { verifyToken, createErrorResponse } from '@/lib/server-utils'

export async function verifyAuthToken(request: NextRequest) {
  try {
    await connectDB()
    
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return { error: createErrorResponse('Token is required', 401) }
    }
    
    const token = authHeader.split(' ')[1]
    if (!token) {
      return { error: createErrorResponse('Token invalid', 401) }
    }
    
    // Verify JWT token
    const decoded = verifyToken(token)
    
    // Check if user exists and token is valid
    const user = await User.findOne({ 
      email: decoded.email, 
      tokenLogin: token, 
      isDeleted: false 
    })
    
    if (!user) {
      return { error: createErrorResponse('The token is expired or not valid.', 401) }
    }
    
    return { user: decoded }
    
  } catch (error: any) {
    return { error: createErrorResponse('The token is expired or not valid.', 401) }
  }
}

export async function requireAuth(request: NextRequest) {
  const authResult = await verifyAuthToken(request)
  
  if (authResult.error) {
    return authResult.error
  }
  
  return null // No error, auth successful
}
