type LogLevel = 'error' | 'warn' | 'info' | 'debug'

interface LogEntry {
  level: LogLevel
  message: string
  timestamp: string
  data?: any
  error?: Error
}

class Logger {
  private logLevel: LogLevel

  constructor() {
    this.logLevel = (process.env.LOG_LEVEL as LogLevel) || 'info'
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    }
    return levels[level] <= levels[this.logLevel]
  }

  private formatLog(entry: LogEntry): string {
    const { level, message, timestamp, data, error } = entry
    let logMessage = `[${timestamp}] ${level.toUpperCase()}: ${message}`
    
    if (data) {
      logMessage += ` | Data: ${JSON.stringify(data)}`
    }
    
    if (error) {
      logMessage += ` | Error: ${error.message}`
      if (error.stack) {
        logMessage += ` | Stack: ${error.stack}`
      }
    }
    
    return logMessage
  }

  private log(level: LogLevel, message: string, data?: any, error?: Error): void {
    if (!this.shouldLog(level)) return

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      data,
      error,
    }

    const formattedLog = this.formatLog(entry)

    // In production, you might want to send logs to external service
    if (process.env.NODE_ENV === 'production') {
      // Send to external logging service (e.g., Winston, Datadog, etc.)
      console.log(formattedLog)
    } else {
      // Development logging with colors
      const colors = {
        error: '\x1b[31m', // Red
        warn: '\x1b[33m',  // Yellow
        info: '\x1b[36m',  // Cyan
        debug: '\x1b[90m', // Gray
      }
      const reset = '\x1b[0m'
      console.log(`${colors[level]}${formattedLog}${reset}`)
    }
  }

  error(message: string, error?: Error, data?: any): void {
    this.log('error', message, data, error)
  }

  warn(message: string, data?: any): void {
    this.log('warn', message, data)
  }

  info(message: string, data?: any): void {
    this.log('info', message, data)
  }

  debug(message: string, data?: any): void {
    this.log('debug', message, data)
  }

  // API request logging
  apiRequest(method: string, path: string, ip?: string, userAgent?: string): void {
    this.info('API Request', {
      method,
      path,
      ip,
      userAgent,
    })
  }

  // API response logging
  apiResponse(method: string, path: string, statusCode: number, duration: number): void {
    const level = statusCode >= 400 ? 'warn' : 'info'
    this.log(level, 'API Response', {
      method,
      path,
      statusCode,
      duration: `${duration}ms`,
    })
  }

  // Database operation logging
  dbOperation(operation: string, collection: string, duration?: number, error?: Error): void {
    if (error) {
      this.error('Database Operation Failed', error, { operation, collection })
    } else {
      this.debug('Database Operation', {
        operation,
        collection,
        duration: duration ? `${duration}ms` : undefined,
      })
    }
  }

  // Authentication logging
  authEvent(event: string, email?: string, ip?: string, success: boolean = true): void {
    const level = success ? 'info' : 'warn'
    this.log(level, `Auth Event: ${event}`, {
      email,
      ip,
      success,
    })
  }
}

export const logger = new Logger()

// Request logging middleware helper
export function logApiRequest(request: Request, startTime: number = Date.now()) {
  const method = request.method
  const url = new URL(request.url)
  const path = url.pathname
  const ip = request.headers.get('x-forwarded-for') || 
             request.headers.get('x-real-ip') || 
             'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'

  logger.apiRequest(method, path, ip, userAgent)

  return {
    logResponse: (response: Response) => {
      const duration = Date.now() - startTime
      logger.apiResponse(method, path, response.status, duration)
    }
  }
}
