'use client'

import { useAuth } from '@/lib/auth-context'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function Home() {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Add a small delay to prevent immediate redirects
    const timer = setTimeout(() => {
      if (!loading) {
        if (isAuthenticated) {
          console.log('Redirecting to dashboard - user is authenticated')
          router.replace('/dashboard')
        } else {
          console.log('Redirecting to login - user not authenticated')
          router.replace('/login')
        }
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [isAuthenticated, loading, router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">GPC Everflow Manager</h1>
        <p className="text-lg mb-8">Loading...</p>
        {/* Debug info */}
        <div className="text-sm text-gray-500">
          Loading: {loading.toString()}, Authenticated: {isAuthenticated.toString()}
        </div>
      </div>
    </div>
  )
}
