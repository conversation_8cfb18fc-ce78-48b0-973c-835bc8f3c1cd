import { NextResponse } from 'next/server'

export async function GET() {
  const apiDocs = {
    openapi: '3.0.0',
    info: {
      title: 'GPC Everflow API',
      version: '1.0.0',
      description: 'Global Performance Commerce Management System API',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production' 
          ? 'https://yourdomain.com/api'
          : 'http://localhost:3000/api',
        description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server'
      }
    ],
    paths: {
      '/health': {
        get: {
          summary: 'Health check',
          description: 'Check the health status of the API',
          responses: {
            '200': {
              description: 'API is healthy',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      status: { type: 'string', example: 'healthy' },
                      timestamp: { type: 'string', format: 'date-time' },
                      uptime: { type: 'number' },
                      environment: { type: 'string' },
                      version: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        }
      },
      '/auth/signin': {
        post: {
          summary: 'User sign in',
          description: 'Authenticate user and return JWT token',
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['email', 'password'],
                  properties: {
                    email: { type: 'string', format: 'email' },
                    password: { type: 'string', minLength: 6 }
                  }
                }
              }
            }
          },
          responses: {
            '200': {
              description: 'Sign in successful',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      success: { type: 'boolean' },
                      data: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string' },
                          email: { type: 'string' },
                          firstName: { type: 'string' },
                          lastName: { type: 'string' },
                          role: { type: 'string' },
                          tokenLogin: { type: 'string' }
                        }
                      },
                      message: { type: 'string' }
                    }
                  }
                }
              }
            },
            '401': {
              description: 'Invalid credentials'
            }
          }
        }
      },
      '/users': {
        get: {
          summary: 'Get all users',
          description: 'Retrieve a list of users with pagination',
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: 'page',
              in: 'query',
              schema: { type: 'integer', minimum: 1, default: 1 }
            },
            {
              name: 'limit',
              in: 'query',
              schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
            },
            {
              name: 'email',
              in: 'query',
              schema: { type: 'string' }
            }
          ],
          responses: {
            '200': {
              description: 'Users retrieved successfully'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        },
        post: {
          summary: 'Create new user',
          description: 'Create a new user account',
          security: [{ bearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['email', 'password', 'firstName', 'lastName'],
                  properties: {
                    email: { type: 'string', format: 'email' },
                    password: { type: 'string', minLength: 6 },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    role: { type: 'string', default: 'user' }
                  }
                }
              }
            }
          },
          responses: {
            '201': {
              description: 'User created successfully'
            },
            '400': {
              description: 'Validation error'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        }
      },
      '/affiliates': {
        get: {
          summary: 'Get all affiliates',
          description: 'Retrieve a list of affiliates with pagination and filtering',
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: 'page',
              in: 'query',
              schema: { type: 'integer', minimum: 1, default: 1 }
            },
            {
              name: 'limit',
              in: 'query',
              schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
            },
            {
              name: 'affId',
              in: 'query',
              schema: { type: 'string' }
            },
            {
              name: 'offerId',
              in: 'query',
              schema: { type: 'string' }
            }
          ],
          responses: {
            '200': {
              description: 'Affiliates retrieved successfully'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        }
      },
      '/error-logs': {
        get: {
          summary: 'Get all error logs',
          description: 'Retrieve a list of error logs with pagination and filtering',
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: 'page',
              in: 'query',
              schema: { type: 'integer', minimum: 1, default: 1 }
            },
            {
              name: 'limit',
              in: 'query',
              schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
            },
            {
              name: 'level',
              in: 'query',
              schema: { type: 'string', enum: ['error', 'warn', 'info', 'debug'] }
            },
            {
              name: 'isResolved',
              in: 'query',
              schema: { type: 'boolean' }
            }
          ],
          responses: {
            '200': {
              description: 'Error logs retrieved successfully'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        },
        post: {
          summary: 'Create error log',
          description: 'Create a new error log entry',
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['message'],
                  properties: {
                    level: { type: 'string', enum: ['error', 'warn', 'info', 'debug'], default: 'error' },
                    message: { type: 'string' },
                    stack: { type: 'string' },
                    url: { type: 'string' },
                    method: { type: 'string' },
                    userAgent: { type: 'string' },
                    ip: { type: 'string' },
                    userId: { type: 'string' },
                    metadata: { type: 'object' }
                  }
                }
              }
            }
          },
          responses: {
            '201': {
              description: 'Error log created successfully'
            },
            '400': {
              description: 'Validation error'
            }
          }
        }
      },
      '/order-infos': {
        get: {
          summary: 'Get all order infos',
          description: 'Retrieve a list of order infos with pagination and filtering',
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: 'page',
              in: 'query',
              schema: { type: 'integer', minimum: 1, default: 1 }
            },
            {
              name: 'limit',
              in: 'query',
              schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
            },
            {
              name: 'orderNumber',
              in: 'query',
              schema: { type: 'string' }
            },
            {
              name: 'status',
              in: 'query',
              schema: { type: 'string', enum: ['pending', 'confirmed', 'cancelled', 'refunded'] }
            }
          ],
          responses: {
            '200': {
              description: 'Order infos retrieved successfully'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        },
        post: {
          summary: 'Create order info',
          description: 'Create a new order info',
          security: [{ bearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['orderNumber', 'affId', 'offerId', 'amount'],
                  properties: {
                    orderNumber: { type: 'string' },
                    affId: { type: 'string' },
                    offerId: { type: 'string' },
                    amount: { type: 'number', minimum: 0 },
                    currency: { type: 'string', default: 'USD' },
                    status: { type: 'string', enum: ['pending', 'confirmed', 'cancelled', 'refunded'], default: 'pending' },
                    customerInfo: {
                      type: 'object',
                      properties: {
                        email: { type: 'string', format: 'email' },
                        firstName: { type: 'string' },
                        lastName: { type: 'string' },
                        phone: { type: 'string' }
                      }
                    }
                  }
                }
              }
            }
          },
          responses: {
            '201': {
              description: 'Order info created successfully'
            },
            '400': {
              description: 'Validation error'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        }
      },
      '/organic-orders': {
        get: {
          summary: 'Get all organic orders',
          description: 'Retrieve a list of organic orders with pagination and filtering',
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: 'page',
              in: 'query',
              schema: { type: 'integer', minimum: 1, default: 1 }
            },
            {
              name: 'limit',
              in: 'query',
              schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
            },
            {
              name: 'affId',
              in: 'query',
              schema: { type: 'string' }
            },
            {
              name: 'offerId',
              in: 'query',
              schema: { type: 'string' }
            }
          ],
          responses: {
            '200': {
              description: 'Organic orders retrieved successfully'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        },
        post: {
          summary: 'Create organic order',
          description: 'Create a new organic order',
          security: [{ bearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['affId', 'offerId', 'orderInfo'],
                  properties: {
                    affId: { type: 'string' },
                    offerId: { type: 'string' },
                    orderInfo: {
                      type: 'object',
                      required: ['orderNumber', 'clickEvent', 'conversionEvent', 'reffererUrl', 'url'],
                      properties: {
                        orderNumber: { type: 'string' },
                        clickEvent: { type: 'string' },
                        conversionEvent: {
                          type: 'object',
                          required: ['transaction_id', 'conversion_id'],
                          properties: {
                            transaction_id: { type: 'string' },
                            conversion_id: { type: 'string' }
                          }
                        },
                        reffererUrl: { type: 'string', format: 'uri' },
                        url: { type: 'string', format: 'uri' }
                      }
                    }
                  }
                }
              }
            }
          },
          responses: {
            '201': {
              description: 'Organic order created successfully'
            },
            '400': {
              description: 'Validation error'
            },
            '401': {
              description: 'Unauthorized'
            }
          }
        }
      }
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  }

  return NextResponse.json(apiDocs, {
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
