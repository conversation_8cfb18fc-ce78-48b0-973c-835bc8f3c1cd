import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { OrganicSale } from '@/models'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const pageURL = searchParams.get('pageURL')
    
    if (!pageURL) {
      return createErrorResponse('pageURL is required', 400)
    }
    
    // Find organic sale by page URL
    const organicSale = await OrganicSale.findOne({
      pageURL,
      isDeleted: false
    })
    
    if (!organicSale) {
      return createSuccessResponse(null, 'No organic sale found for this URL')
    }
    
    return createSuccessResponse(organicSale, 'Organic sale found')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
