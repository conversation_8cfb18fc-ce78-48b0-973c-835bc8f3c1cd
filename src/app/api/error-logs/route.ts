import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { ErrorLog } from '@/models'
import { errorLogSchema, errorLogQuerySchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse, getPaginationParams, createPaginationInfo } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    const { level, isResolved, userId } = errorLogQuerySchema.parse(Object.fromEntries(searchParams))
    
    // Build filters
    const filters: any = {}
    if (level) {
      filters.level = level
    }
    if (isResolved !== undefined) {
      filters.isResolved = isResolved
    }
    if (userId) {
      filters.userId = userId
    }
    
    // Get data and total count
    const [errorLogs, totalRecords] = await Promise.all([
      ErrorLog.find(filters)
        .skip(skip)
        .limit(limit)
        .sort({ timestamp: -1 }),
      ErrorLog.countDocuments(filters)
    ])
    
    const pagination = createPaginationInfo(page, limit, totalRecords)
    
    return createSuccessResponse(errorLogs, 'Data retrieved successfully', pagination)
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = errorLogSchema.parse(body)
    
    // Convert date strings to Date objects
    if (typeof validatedData.timestamp === 'string') {
      validatedData.timestamp = new Date(validatedData.timestamp)
    }
    
    // Create error log
    const errorLog = new ErrorLog(validatedData)
    const savedErrorLog = await errorLog.save()
    
    return createSuccessResponse(savedErrorLog, 'Error log created successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
