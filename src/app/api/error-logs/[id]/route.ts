import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { ErrorLog } from '@/models'
import { errorLogUpdateSchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const errorLog = await ErrorLog.findById(params.id)
    
    if (!errorLog) {
      return createErrorResponse('Error log not found', 404)
    }
    
    return createSuccessResponse(errorLog, 'Data retrieved successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = errorLogUpdateSchema.parse(body)
    
    // Convert date strings to Date objects
    if (validatedData.resolvedAt && typeof validatedData.resolvedAt === 'string') {
      validatedData.resolvedAt = new Date(validatedData.resolvedAt)
    }
    
    const updatedErrorLog = await ErrorLog.findByIdAndUpdate(
      params.id,
      validatedData,
      { new: true }
    )
    
    if (!updatedErrorLog) {
      return createErrorResponse('Error log not found', 404)
    }
    
    return createSuccessResponse(updatedErrorLog, 'Error log updated successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const deletedErrorLog = await ErrorLog.findByIdAndDelete(params.id)
    
    if (!deletedErrorLog) {
      return createErrorResponse('Error log not found', 404)
    }
    
    return createSuccessResponse(null, 'Error log deleted successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
