import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { Affiliate } from '@/models'
import { affiliateUpdateSchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const affiliate = await Affiliate.findOne({
      _id: params.id,
      isDeleted: false
    })
    
    if (!affiliate) {
      return createErrorResponse('Affiliate not found', 404)
    }
    
    return createSuccessResponse(affiliate, 'Data retrieved successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = affiliateUpdateSchema.parse(body)
    
    // Convert date strings to Date objects
    if (validatedData.startDate && typeof validatedData.startDate === 'string') {
      validatedData.startDate = new Date(validatedData.startDate)
    }
    if (validatedData.endDate && typeof validatedData.endDate === 'string') {
      validatedData.endDate = new Date(validatedData.endDate)
    }
    
    const updatedAffiliate = await Affiliate.findOneAndUpdate(
      { _id: params.id, isDeleted: false },
      validatedData,
      { new: true }
    )
    
    if (!updatedAffiliate) {
      return createErrorResponse('Affiliate not found', 404)
    }
    
    return createSuccessResponse(updatedAffiliate, 'Affiliate updated successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const deletedAffiliate = await Affiliate.findOneAndUpdate(
      { _id: params.id, isDeleted: false },
      { isDeleted: true },
      { new: true }
    )
    
    if (!deletedAffiliate) {
      return createErrorResponse('Affiliate not found', 404)
    }
    
    return createSuccessResponse(null, 'Affiliate deleted successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
