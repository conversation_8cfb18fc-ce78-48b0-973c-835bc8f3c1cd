import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { Affiliate } from '@/models'
import { affiliateSchema, affiliateQuerySchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse, getPaginationParams, createPaginationInfo } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    const { affId, offerId } = affiliateQuerySchema.parse(Object.fromEntries(searchParams))
    
    // Build filters
    const filters: any = { isDeleted: false }
    if (affId) {
      filters.affId = new RegExp(affId, 'i')
    }
    if (offerId) {
      filters.offerId = new RegExp(offerId, 'i')
    }
    
    // Get data and total count
    const [affiliates, totalRecords] = await Promise.all([
      Affiliate.find(filters)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      Affiliate.countDocuments(filters)
    ])
    
    const pagination = createPaginationInfo(page, limit, totalRecords)
    
    return createSuccessResponse(affiliates, 'Data retrieved successfully', pagination)
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.errors[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function POST(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = affiliateSchema.parse(body)
    
    // Convert date strings to Date objects
    if (typeof validatedData.startDate === 'string') {
      validatedData.startDate = new Date(validatedData.startDate)
    }
    if (typeof validatedData.endDate === 'string') {
      validatedData.endDate = new Date(validatedData.endDate)
    }
    
    // Create affiliate
    const affiliate = new Affiliate(validatedData)
    const savedAffiliate = await affiliate.save()
    
    return createSuccessResponse(savedAffiliate, 'Resource created successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.errors[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
