import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // For now, return mock data until we fix the database connection
    const total = 5 // Mock data

    return NextResponse.json({
      total,
      success: true
    })
  } catch (error) {
    console.error('Error fetching users stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users statistics', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
