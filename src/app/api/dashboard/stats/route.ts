import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/mongodb'
import User from '@/models/User'
import Affiliate from '@/models/Affiliate'
import OrganicSale from '@/models/OrganicSale'
import { requireAuth } from '@/lib/auth'

async function dashboardStatsHandler(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) {
    return authError
  }

  try {
    await connectDB()

    // Get basic counts first
    const [
      totalUsers,
      totalAffiliates,
      totalOrganicSales
    ] = await Promise.all([
      // Count active users
      User.countDocuments({ isDeleted: false }),

      // Count active affiliates
      Affiliate.countDocuments({ isDeleted: false }),

      // Count organic sales
      OrganicSale.countDocuments({ isDeleted: false })
    ])

    // Get recent activity stats (30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const [
      newUsersThisMonth,
      newAffiliatesThisMonth
    ] = await Promise.all([
      User.countDocuments({
        isDeleted: false,
        createdAt: { $gte: thirtyDaysAgo }
      }),

      Affiliate.countDocuments({
        isDeleted: false,
        createdAt: { $gte: thirtyDaysAgo }
      })
    ])

    // Get affiliate performance stats
    const affiliateStats = await Affiliate.aggregate([
      {
        $match: { isDeleted: false }
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: "$orders" },
          totalNotFired: { $sum: "$total_not_fired" },
          avgRate: { $avg: "$rate" },
          activeAffiliates: {
            $sum: {
              $cond: [{ $eq: ["$isPaused", false] }, 1, 0]
            }
          }
        }
      }
    ])

    const affiliateData = affiliateStats.length > 0 ? affiliateStats[0] : {
      totalOrders: 0,
      totalNotFired: 0,
      avgRate: 0,
      activeAffiliates: 0
    }

    return NextResponse.json({
      success: true,
      data: {
        // Main stats
        totalUsers,
        totalAffiliates,
        totalOrganicSales,
        totalErrorLogs: 0, // Mock for now
        unresolvedErrorLogs: 0, // Mock for now

        // Recent activity (30 days)
        recentActivity: {
          newUsers: newUsersThisMonth,
          newAffiliates: newAffiliatesThisMonth,
          recentSales: 0, // Mock for now
          recentErrors: 0 // Mock for now
        },

        // Revenue stats (mock for now)
        revenue: {
          total: 0,
          orders: 0,
          avgOrderValue: 0
        },

        // Affiliate performance
        affiliatePerformance: {
          totalOrders: affiliateData.totalOrders,
          totalNotFired: affiliateData.totalNotFired,
          avgRate: affiliateData.avgRate,
          activeAffiliates: affiliateData.activeAffiliates,
          conversionRate: affiliateData.totalOrders > 0
            ? ((affiliateData.totalOrders - affiliateData.totalNotFired) / affiliateData.totalOrders * 100)
            : 0
        }
      }
    })

  } catch (error: any) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch dashboard statistics',
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return dashboardStatsHandler(request)
}
