import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { User } from '@/models'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return createErrorResponse('Token is required', 400)
    }
    
    const token = authHeader.split(' ')[1]
    if (!token) {
      return createErrorResponse('Token invalid', 400)
    }
    
    const user = await User.findOne({ tokenLogin: token })
    if (!user) {
      return createErrorResponse('The token is not correct.', 400)
    }
    
    await User.findOneAndUpdate(
      { tokenLogin: token },
      { tokenLogin: '' },
      { new: true }
    )
    
    return createSuccessResponse(null, 'Sign out successful')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
