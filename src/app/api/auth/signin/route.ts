import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { User } from '@/models'
import { signInSchema } from '@/lib/validations'
import { comparePassword, encodeToken, createErrorResponse, createSuccessResponse } from '@/lib/server-utils'
import { withApiMiddleware, validateRequestMethod, validateContentType } from '@/lib/api-middleware'

export const POST = withApiMiddleware(async (request: NextRequest) => {
  // Validate request method and content type
  const methodError = validateRequestMethod(['POST'])(request)
  if (methodError) return methodError

  const contentTypeError = validateContentType(request)
  if (contentTypeError) return contentTypeError
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = signInSchema.parse(body)
    
    const user = await User.findOne({ 
      email: validatedData.email, 
      isDeleted: false 
    })
    
    if (!user) {
      return createErrorResponse('The username or password is not correct.', 401)
    }
    
    const isPasswordValid = await comparePassword(validatedData.password, user.password)
    if (!isPasswordValid) {
      return createErrorResponse('The username or password is not correct.', 401)
    }
    
    const token = encodeToken({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role
    })
    
    const updatedUser = await User.findOneAndUpdate(
      { _id: user._id },
      { tokenLogin: token },
      { new: true }
    ).select(['_id', 'email', 'firstName', 'lastName', 'role', 'tokenLogin'])
    
    return createSuccessResponse(updatedUser, 'Sign in successful')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.errors[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
})
