import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/mongodb'
import User from '@/models/User'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

// User registration schema
const userSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.enum(['user', 'admin', 'manager']).optional().default('user'),
})

export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = userSchema.parse(body)
    
    // Check if user already exists
    const existingUser = await User.findOne({ 
      email: validatedData.email, 
      isDeleted: false 
    })
    
    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: 'The user already exists.'
      }, { status: 400 })
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user with default role 'user'
    const user = new User({
      ...validatedData,
      password: hashedPassword,
      role: validatedData.role || 'user', // Default to 'user' role
    })

    const savedUser = await user.save()

    // Remove password from response
    const userResponse = {
      _id: savedUser._id,
      email: savedUser.email,
      firstName: savedUser.firstName,
      lastName: savedUser.lastName,
      role: savedUser.role,
      createdAt: savedUser.createdAt,
    }

    return NextResponse.json({
      success: true,
      data: userResponse,
      message: 'Registration successful'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.issues[0].message
      }, { status: 400 })
    }
    if ((error as any).code === 11000) {
      return NextResponse.json({
        success: false,
        message: 'Email already exists'
      }, { status: 400 })
    }
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}
