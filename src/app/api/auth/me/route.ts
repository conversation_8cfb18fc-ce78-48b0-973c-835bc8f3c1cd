import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/db'
import User from '@/models/User'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    // Get user ID from the auth middleware
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return createErrorResponse('Invalid authorization header', 401)
    }

    const token = authHeader.substring(7)
    const jwt = require('jsonwebtoken')

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET) as any

      const user = await User.findOne({
        _id: decoded.userId,
        isDeleted: false
      }).select('-password')

      if (!user) {
        return createErrorResponse('User not found', 404)
      }

      return createSuccessResponse(user, 'User data retrieved successfully')

    } catch (jwtError) {
      return createErrorResponse('Invalid token', 401)
    }

  } catch (error: any) {
    console.error('Get current user error:', error)
    return createErrorResponse('Failed to fetch user data', 500)
  }
}
