import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // Check database connection
    await connectDB()
    
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      checks: {
        database: 'connected',
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          unit: 'MB'
        },
        responseTime: `${Date.now() - startTime}ms`
      }
    }
    
    logger.info('Health check completed', healthCheck)
    
    return NextResponse.json(healthCheck, { status: 200 })
    
  } catch (error: any) {
    const healthCheck = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      error: error.message,
      checks: {
        database: 'disconnected',
        responseTime: `${Date.now() - startTime}ms`
      }
    }
    
    logger.error('Health check failed', error, healthCheck)
    
    return NextResponse.json(healthCheck, { status: 503 })
  }
}
