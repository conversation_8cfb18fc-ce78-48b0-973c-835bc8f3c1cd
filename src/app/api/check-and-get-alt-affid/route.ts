import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { Affiliate, AlternateAffiliate } from '@/models'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const affId = searchParams.get('affId')
    const offerId = searchParams.get('offerId')
    
    if (!affId || !offerId) {
      return createErrorResponse('affId and offerId are required', 400)
    }
    
    // Find affiliate
    const affiliate = await Affiliate.findOne({
      affId,
      offerId,
      isDeleted: false,
      isPaused: false
    }).populate('altAffs')
    
    if (!affiliate) {
      return createSuccessResponse(null, 'No affiliate found')
    }
    
    // Check if affiliate has alternate affiliates
    if (!affiliate.altAffs || affiliate.altAffs.length === 0) {
      return createSuccessResponse({
        affiliate,
        alternateAffiliate: null
      }, 'No alternate affiliate available')
    }
    
    // Find an active alternate affiliate
    const activeAltAff = affiliate.altAffs.find((altAff: any) => 
      !altAff.isPaused && !altAff.isDeleted
    )
    
    if (!activeAltAff) {
      return createSuccessResponse({
        affiliate,
        alternateAffiliate: null
      }, 'No active alternate affiliate available')
    }
    
    return createSuccessResponse({
      affiliate,
      alternateAffiliate: activeAltAff
    }, 'Alternate affiliate found')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
