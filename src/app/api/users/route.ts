import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { User } from '@/models'
import { userSchema, userQuerySchema } from '@/lib/validations'
import { hashPassword, createErrorResponse, createSuccessResponse, getPaginationParams, createPaginationInfo } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    const { email } = userQuerySchema.parse(Object.fromEntries(searchParams))
    
    // Build filters
    const filters: any = { isDeleted: false }
    if (email) {
      filters.email = new RegExp(email, 'i')
    }
    
    // Get data and total count
    const [users, totalRecords] = await Promise.all([
      User.find(filters)
        .select('-password -tokenLogin')
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      User.countDocuments(filters)
    ])
    
    const pagination = createPaginationInfo(page, limit, totalRecords)
    
    return createSuccessResponse(users, 'Data retrieved successfully', pagination)
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.errors[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function POST(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = userSchema.parse(body)
    
    // Check if user already exists
    const existingUser = await User.findOne({ 
      email: validatedData.email, 
      isDeleted: false 
    })
    
    if (existingUser) {
      return createErrorResponse('The user already exists.', 400)
    }
    
    // Hash password
    const hashedPassword = await hashPassword(validatedData.password)
    
    // Create user
    const user = new User({
      ...validatedData,
      password: hashedPassword,
    })
    
    const savedUser = await user.save()
    
    // Remove sensitive data from response
    const userResponse = {
      _id: savedUser._id,
      email: savedUser.email,
      firstName: savedUser.firstName,
      lastName: savedUser.lastName,
      role: savedUser.role,
      createdAt: savedUser.createdAt,
      updatedAt: savedUser.updatedAt,
    }
    
    return createSuccessResponse(userResponse, 'Resource created successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.errors[0].message, 400)
    }
    if (error.code === 11000) {
      return createErrorResponse('Email already exists', 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
