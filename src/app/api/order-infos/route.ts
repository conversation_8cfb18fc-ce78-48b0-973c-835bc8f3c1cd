import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { OrderInfo } from '@/models'
import { orderInfoSchema, orderInfoQuerySchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse, getPaginationParams, createPaginationInfo } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    const { orderNumber, affId, offerId, status } = orderInfoQuerySchema.parse(Object.fromEntries(searchParams))
    
    // Build filters
    const filters: any = {}
    if (orderNumber) {
      filters.orderNumber = new RegExp(orderNumber, 'i')
    }
    if (affId) {
      filters.affId = new RegExp(affId, 'i')
    }
    if (offerId) {
      filters.offerId = new RegExp(offerId, 'i')
    }
    if (status) {
      filters.status = status
    }
    
    // Get data and total count
    const [orderInfos, totalRecords] = await Promise.all([
      OrderInfo.find(filters)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      OrderInfo.countDocuments(filters)
    ])
    
    const pagination = createPaginationInfo(page, limit, totalRecords)
    
    return createSuccessResponse(orderInfos, 'Data retrieved successfully', pagination)
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function POST(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = orderInfoSchema.parse(body)
    
    // Check if order already exists
    const existingOrder = await OrderInfo.findOne({ 
      orderNumber: validatedData.orderNumber 
    })
    
    if (existingOrder) {
      return createErrorResponse('Order with this number already exists', 400)
    }
    
    // Create order info
    const orderInfo = new OrderInfo(validatedData)
    const savedOrderInfo = await orderInfo.save()
    
    return createSuccessResponse(savedOrderInfo, 'Order info created successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    if (error.code === 11000) {
      return createErrorResponse('Order number already exists', 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
