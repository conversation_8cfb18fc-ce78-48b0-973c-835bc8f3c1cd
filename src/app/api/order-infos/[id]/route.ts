import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { OrderInfo } from '@/models'
import { orderInfoSchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const orderInfo = await OrderInfo.findById(params.id)
    
    if (!orderInfo) {
      return createErrorResponse('Order info not found', 404)
    }
    
    return createSuccessResponse(orderInfo, 'Data retrieved successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    // Create a partial schema for updates
    const updateSchema = orderInfoSchema.partial()
    const validatedData = updateSchema.parse(body)
    
    // Check if order number is being updated and already exists
    if (validatedData.orderNumber) {
      const existingOrder = await OrderInfo.findOne({
        orderNumber: validatedData.orderNumber,
        _id: { $ne: params.id }
      })
      
      if (existingOrder) {
        return createErrorResponse('Order number already exists', 400)
      }
    }
    
    const updatedOrderInfo = await OrderInfo.findByIdAndUpdate(
      params.id,
      validatedData,
      { new: true }
    )
    
    if (!updatedOrderInfo) {
      return createErrorResponse('Order info not found', 404)
    }
    
    return createSuccessResponse(updatedOrderInfo, 'Order info updated successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const deletedOrderInfo = await OrderInfo.findByIdAndDelete(params.id)
    
    if (!deletedOrderInfo) {
      return createErrorResponse('Order info not found', 404)
    }
    
    return createSuccessResponse(null, 'Order info deleted successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
