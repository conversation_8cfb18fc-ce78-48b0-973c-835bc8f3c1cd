import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { OrganicOrder } from '@/models'
import { organicOrderSchema, affiliateQuerySchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse, getPaginationParams, createPaginationInfo } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    const { affId, offerId } = affiliateQuerySchema.parse(Object.fromEntries(searchParams))
    
    // Build filters
    const filters: any = { isDeleted: false }
    if (affId) {
      filters.affId = new RegExp(affId, 'i')
    }
    if (offerId) {
      filters.offerId = new RegExp(offerId, 'i')
    }
    
    // Get data and total count
    const [organicOrders, totalRecords] = await Promise.all([
      OrganicOrder.find(filters)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      OrganicOrder.countDocuments(filters)
    ])
    
    const pagination = createPaginationInfo(page, limit, totalRecords)
    
    return createSuccessResponse(organicOrders, 'Data retrieved successfully', pagination)
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function POST(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = organicOrderSchema.parse(body)
    
    // Check if order already exists
    const existingOrder = await OrganicOrder.findOne({ 
      'orderInfo.orderNumber': validatedData.orderInfo.orderNumber,
      isDeleted: false
    })
    
    if (existingOrder) {
      return createErrorResponse('Order with this number already exists', 400)
    }
    
    // Create organic order
    const organicOrder = new OrganicOrder(validatedData)
    const savedOrganicOrder = await organicOrder.save()
    
    return createSuccessResponse(savedOrganicOrder, 'Organic order created successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
