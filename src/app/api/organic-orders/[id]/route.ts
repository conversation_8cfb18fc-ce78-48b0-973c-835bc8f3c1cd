import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { OrganicOrder } from '@/models'
import { organicOrderSchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const organicOrder = await OrganicOrder.findOne({ 
      _id: params.id, 
      isDeleted: false 
    })
    
    if (!organicOrder) {
      return createErrorResponse('Organic order not found', 404)
    }
    
    return createSuccessResponse(organicOrder, 'Data retrieved successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = organicOrderSchema.partial().parse(body)
    
    // Check if order number is being updated and already exists
    if (validatedData.orderInfo?.orderNumber) {
      const existingOrder = await OrganicOrder.findOne({
        'orderInfo.orderNumber': validatedData.orderInfo.orderNumber,
        _id: { $ne: params.id },
        isDeleted: false
      })
      
      if (existingOrder) {
        return createErrorResponse('Order number already exists', 400)
      }
    }
    
    const updatedOrganicOrder = await OrganicOrder.findOneAndUpdate(
      { _id: params.id, isDeleted: false },
      validatedData,
      { new: true }
    )
    
    if (!updatedOrganicOrder) {
      return createErrorResponse('Organic order not found', 404)
    }
    
    return createSuccessResponse(updatedOrganicOrder, 'Organic order updated successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const deletedOrganicOrder = await OrganicOrder.findOneAndUpdate(
      { _id: params.id, isDeleted: false },
      { isDeleted: true },
      { new: true }
    )
    
    if (!deletedOrganicOrder) {
      return createErrorResponse('Organic order not found', 404)
    }
    
    return createSuccessResponse(null, 'Organic order deleted successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
