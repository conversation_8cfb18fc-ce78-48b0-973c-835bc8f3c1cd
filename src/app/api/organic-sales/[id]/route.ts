import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { OrganicSale } from '@/models'
import { organicSaleSchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const organicSale = await OrganicSale.findOne({ 
      _id: params.id, 
      isDeleted: false 
    })
    
    if (!organicSale) {
      return createErrorResponse('Organic sale not found', 404)
    }
    
    return createSuccessResponse(organicSale, 'Data retrieved successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = organicSaleSchema.parse(body)
    
    // Check if URL is being updated and already exists
    const existingOrganicSale = await OrganicSale.findOne({
      pageURL: validatedData.pageURL,
      _id: { $ne: params.id },
      isDeleted: false
    })
    
    if (existingOrganicSale) {
      return createErrorResponse('Organic sale with this URL already exists', 400)
    }
    
    const updatedOrganicSale = await OrganicSale.findOneAndUpdate(
      { _id: params.id, isDeleted: false },
      validatedData,
      { new: true }
    )
    
    if (!updatedOrganicSale) {
      return createErrorResponse('Organic sale not found', 404)
    }
    
    return createSuccessResponse(updatedOrganicSale, 'Organic sale updated successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.issues[0].message, 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const deletedOrganicSale = await OrganicSale.findOneAndUpdate(
      { _id: params.id, isDeleted: false },
      { isDeleted: true },
      { new: true }
    )
    
    if (!deletedOrganicSale) {
      return createErrorResponse('Organic sale not found', 404)
    }
    
    return createSuccessResponse(null, 'Organic sale deleted successfully')
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
