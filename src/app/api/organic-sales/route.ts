import { NextRequest } from 'next/server'
import connectDB from '@/lib/db'
import { OrganicSale } from '@/models'
import { organicSaleSchema } from '@/lib/validations'
import { createErrorResponse, createSuccessResponse, getPaginationParams, createPaginationInfo } from '@/lib/server-utils'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = getPaginationParams(searchParams)
    
    // Build filters
    const filters: any = { isDeleted: false }
    
    // Get data and total count
    const [organicSales, totalRecords] = await Promise.all([
      OrganicSale.find(filters)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      OrganicSale.countDocuments(filters)
    ])
    
    const pagination = createPaginationInfo(page, limit, totalRecords)
    
    return createSuccessResponse(organicSales, 'Data retrieved successfully', pagination)
    
  } catch (error: any) {
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}

export async function POST(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) return authError
  
  try {
    await connectDB()
    
    const body = await request.json()
    const validatedData = organicSaleSchema.parse(body)
    
    // Check if organic sale already exists
    const existingOrganicSale = await OrganicSale.findOne({ 
      pageURL: validatedData.pageURL, 
      isDeleted: false 
    })
    
    if (existingOrganicSale) {
      return createErrorResponse('Organic sale with this URL already exists', 400)
    }
    
    // Create organic sale
    const organicSale = new OrganicSale(validatedData)
    const savedOrganicSale = await organicSale.save()
    
    return createSuccessResponse(savedOrganicSale, 'Resource created successfully')
    
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return createErrorResponse(error.errors[0].message, 400)
    }
    if (error.code === 11000) {
      return createErrorResponse('Organic sale with this URL already exists', 400)
    }
    return createErrorResponse(error.message || 'Internal server error', 500)
  }
}
