'use client'

import { useState, useEffect } from 'react'
import { api } from '@/lib/api-client'

interface DashboardStats {
  totalUsers: number
  totalAffiliates: number
  totalOrganicSales: number
  totalErrorLogs: number
  unresolvedErrorLogs: number
  recentActivity: {
    newUsers: number
    newAffiliates: number
    recentSales: number
    recentErrors: number
  }
  revenue: {
    total: number
    orders: number
    avgOrderValue: number
  }
  affiliatePerformance: {
    totalOrders: number
    totalNotFired: number
    avgRate: number
    activeAffiliates: number
    conversionRate: number
  }
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalAffiliates: 0,
    totalOrganicSales: 0,
    totalErrorLogs: 0,
    unresolvedErrorLogs: 0,
    recentActivity: {
      newUsers: 0,
      newAffiliates: 0,
      recentSales: 0,
      recentErrors: 0
    },
    revenue: {
      total: 0,
      orders: 0,
      avgOrderValue: 0
    },
    affiliatePerformance: {
      totalOrders: 0,
      totalNotFired: 0,
      avgRate: 0,
      activeAffiliates: 0,
      conversionRate: 0
    }
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        setError('')

        const result = await api.get('/api/dashboard/stats')

        if (result.success) {
          setStats(result.data as DashboardStats)
        } else {
          throw new Error(result.message || 'Failed to fetch dashboard stats')
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
        setError(error instanceof Error ? error.message : 'Failed to load dashboard data')

        // Keep default empty stats on error
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  return (
    <div className="container-fluid py-4">
      {/* Welcome Section */}
      <div className="mb-4">
        <h2 className="mb-2">Dashboard</h2>
        <p className="text-muted">
          Welcome to the Global Performance Commerce Management System
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="alert alert-danger mb-4" role="alert">
          <i className="bi bi-exclamation-triangle-fill me-2"></i>
          {error}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="row g-4 mb-4">
        <div className="col-md-3">
          <div className="card text-center h-100">
            <div className="card-body">
              <i className="bi bi-people-fill text-success mb-3" style={{fontSize: '2.5rem'}}></i>
              <h6 className="card-title text-muted">Total Users</h6>
              {loading ? (
                <div className="spinner-border spinner-border-sm text-success" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <h3 className="text-success">{stats.totalUsers.toLocaleString()}</h3>
              )}
              <small className="text-muted">Active users in system</small>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="card text-center h-100">
            <div className="card-body">
              <i className="bi bi-diagram-3 text-primary mb-3" style={{fontSize: '2.5rem'}}></i>
              <h6 className="card-title text-muted">Total Affiliates</h6>
              {loading ? (
                <div className="spinner-border spinner-border-sm text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <h3 className="text-primary">{stats.totalAffiliates.toLocaleString()}</h3>
              )}
              <small className="text-muted">Registered affiliate partners</small>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="card text-center h-100">
            <div className="card-body">
              <i className="bi bi-cart-fill text-info mb-3" style={{fontSize: '2.5rem'}}></i>
              <h6 className="card-title text-muted">Organic Sales</h6>
              {loading ? (
                <div className="spinner-border spinner-border-sm text-info" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <h3 className="text-info">{stats.totalOrganicSales.toLocaleString()}</h3>
              )}
              <small className="text-muted">Total organic sales tracked</small>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="card text-center h-100">
            <div className="card-body">
              <i className="bi bi-exclamation-triangle-fill text-danger mb-3" style={{fontSize: '2.5rem'}}></i>
              <h6 className="card-title text-muted">Error Logs</h6>
              {loading ? (
                <div className="spinner-border spinner-border-sm text-danger" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <div>
                  <h3 className="text-danger">{stats.unresolvedErrorLogs.toLocaleString()}</h3>
                  <small className="text-muted">
                    {stats.unresolvedErrorLogs} unresolved / {stats.totalErrorLogs} total
                  </small>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Stats Row */}
      <div className="row g-4 mb-4">
        <div className="col-md-4">
          <div className="card text-center h-100">
            <div className="card-body">
              <i className="bi bi-currency-dollar text-warning mb-3" style={{fontSize: '2.5rem'}}></i>
              <h6 className="card-title text-muted">Total Revenue</h6>
              {loading ? (
                <div className="spinner-border spinner-border-sm text-warning" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <div>
                  <h3 className="text-warning">${stats.revenue.total.toLocaleString()}</h3>
                  <small className="text-muted">
                    {stats.revenue.orders} orders • Avg: ${stats.revenue.avgOrderValue.toFixed(2)}
                  </small>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-md-4">
          <div className="card text-center h-100">
            <div className="card-body">
              <i className="bi bi-graph-up text-success mb-3" style={{fontSize: '2.5rem'}}></i>
              <h6 className="card-title text-muted">Conversion Rate</h6>
              {loading ? (
                <div className="spinner-border spinner-border-sm text-success" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <div>
                  <h3 className="text-success">{stats.affiliatePerformance.conversionRate.toFixed(1)}%</h3>
                  <small className="text-muted">
                    {stats.affiliatePerformance.activeAffiliates} active affiliates
                  </small>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-md-4">
          <div className="card text-center h-100">
            <div className="card-body">
              <i className="bi bi-clock-history text-info mb-3" style={{fontSize: '2.5rem'}}></i>
              <h6 className="card-title text-muted">Recent Activity (30 days)</h6>
              {loading ? (
                <div className="spinner-border spinner-border-sm text-info" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <div>
                  <h3 className="text-info">{stats.recentActivity.newUsers + stats.recentActivity.newAffiliates}</h3>
                  <small className="text-muted">
                    {stats.recentActivity.newUsers} users • {stats.recentActivity.newAffiliates} affiliates
                  </small>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Quick Actions</h5>
        </div>
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-4">
              <a href="/dashboard/users" className="text-decoration-none">
                <div className="card border-primary h-100 hover-shadow">
                  <div className="card-body text-center">
                    <i className="bi bi-people-fill text-primary mb-3" style={{fontSize: '3rem'}}></i>
                    <h6 className="card-title">Manage Users</h6>
                    <p className="card-text text-muted small">Create and manage user accounts</p>
                  </div>
                </div>
              </a>
            </div>
            <div className="col-md-4">
              <a href="/affiliates" className="text-decoration-none">
                <div className="card border-success h-100 hover-shadow">
                  <div className="card-body text-center">
                    <i className="bi bi-diagram-3 text-success mb-3" style={{fontSize: '3rem'}}></i>
                    <h6 className="card-title">Manage Affiliates</h6>
                    <p className="card-text text-muted small">Register and manage affiliate partners</p>
                  </div>
                </div>
              </a>
            </div>
            <div className="col-md-4">
              <a href="/organic-sales" className="text-decoration-none">
                <div className="card border-info h-100 hover-shadow">
                  <div className="card-body text-center">
                    <i className="bi bi-cart-fill text-info mb-3" style={{fontSize: '3rem'}}></i>
                    <h6 className="card-title">Organic Sales</h6>
                    <p className="card-text text-muted small">Track and manage organic sales</p>
                  </div>
                </div>
              </a>
            </div>
            <div className="col-md-4">
              <a href="/organic-orders" className="text-decoration-none">
                <div className="card border-warning h-100 hover-shadow">
                  <div className="card-body text-center">
                    <i className="bi bi-box-seam text-warning mb-3" style={{fontSize: '3rem'}}></i>
                    <h6 className="card-title">Organic Orders</h6>
                    <p className="card-text text-muted small">Manage organic order tracking</p>
                  </div>
                </div>
              </a>
            </div>
            <div className="col-md-4">
              <a href="/order-infos" className="text-decoration-none">
                <div className="card border-secondary h-100 hover-shadow">
                  <div className="card-body text-center">
                    <i className="bi bi-file-text text-secondary mb-3" style={{fontSize: '3rem'}}></i>
                    <h6 className="card-title">Order Infos</h6>
                    <p className="card-text text-muted small">View and manage order information</p>
                  </div>
                </div>
              </a>
            </div>
            <div className="col-md-4">
              <a href="/error-logs" className="text-decoration-none">
                <div className="card border-danger h-100 hover-shadow">
                  <div className="card-body text-center">
                    <i className="bi bi-exclamation-triangle-fill text-danger mb-3" style={{fontSize: '3rem'}}></i>
                    <h6 className="card-title">Error Logs</h6>
                    <p className="card-text text-muted small">Monitor and resolve system errors</p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}


