import mongoose, { Document, Schema } from 'mongoose'

export interface IOrganicSale extends Document {
  pageURL: string
  affId: string
  offerId: string
  amount: number
  currency: string
  status: 'pending' | 'confirmed' | 'cancelled' | 'refunded'
  customerInfo: {
    email: string
    firstName: string
    lastName: string
    phone?: string
  }
  orderInfo: {
    orderNumber: string
    clickEvent: string
    conversionEvent: {
      eventType: string
      timestamp: Date
    }
  }
  isPaused: boolean
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
}

const OrganicSaleSchema = new Schema<IOrganicSale>({
  pageURL: { type: String, required: true },
  affId: { type: String, required: true },
  offerId: { type: String, required: true },
  amount: { type: Number, required: true, min: 0 },
  currency: { type: String, required: true, default: 'USD' },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'confirmed', 'cancelled', 'refunded'],
    default: 'pending'
  },
  customerInfo: {
    email: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    phone: { type: String }
  },
  orderInfo: {
    orderNumber: { type: String, required: true },
    clickEvent: { type: String, required: true },
    conversionEvent: {
      eventType: { type: String, required: true },
      timestamp: { type: Date, required: true }
    }
  },
  isPaused: { type: Boolean, default: false },
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: true,
  collection: 'organicsales'
})

// Indexes
OrganicSaleSchema.index({ affId: 1, offerId: 1 })
OrganicSaleSchema.index({ isDeleted: 1 })
OrganicSaleSchema.index({ isPaused: 1 })
OrganicSaleSchema.index({ status: 1 })
OrganicSaleSchema.index({ 'orderInfo.orderNumber': 1 })

export default mongoose.models.OrganicSale || mongoose.model<IOrganicSale>('OrganicSale', OrganicSaleSchema)
