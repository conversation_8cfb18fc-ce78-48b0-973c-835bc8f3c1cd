import mongoose, { Document, Schema } from 'mongoose'

export interface IErrorLog extends Document {
  level: 'error' | 'warn' | 'info' | 'debug'
  message: string
  stack?: string
  url?: string
  method?: string
  userAgent?: string
  ip?: string
  userId?: string
  timestamp: Date
  metadata?: Record<string, unknown>
  isResolved: boolean
  resolvedBy?: string
  resolvedAt?: Date
  createdAt: Date
  updatedAt: Date
}

const ErrorLogSchema = new Schema<IErrorLog>({
  level: { 
    type: String, 
    required: true, 
    enum: ['error', 'warn', 'info', 'debug'],
    default: 'error'
  },
  message: { type: String, required: true },
  stack: { type: String },
  url: { type: String },
  method: { type: String },
  userAgent: { type: String },
  ip: { type: String },
  userId: { type: String },
  timestamp: { type: Date, default: Date.now },
  metadata: { type: Schema.Types.Mixed },
  isResolved: { type: Boolean, default: false },
  resolvedBy: { type: String },
  resolvedAt: { type: Date },
}, { 
  timestamps: true,
  collection: 'errorlogs'
})

// Indexes
ErrorLogSchema.index({ level: 1 })
ErrorLogSchema.index({ timestamp: -1 })
ErrorLogSchema.index({ isResolved: 1 })
ErrorLogSchema.index({ userId: 1 })
ErrorLogSchema.index({ url: 1, method: 1 })

export default mongoose.models.ErrorLog || mongoose.model<IErrorLog>('ErrorLog', ErrorLogSchema)
