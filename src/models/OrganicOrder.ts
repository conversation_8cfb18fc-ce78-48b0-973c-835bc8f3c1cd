import mongoose, { Document, Schema } from 'mongoose'

export interface IOrderInfo {
  orderNumber: string
  clickEvent: string
  conversionEvent: {
    transaction_id: string
    conversion_id: string
  }
  reffererUrl: string
  url: string
}

export interface IOrganicOrder extends Document {
  affId: string
  offerId: string
  orderInfo: IOrderInfo
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
}

const OrganicOrderSchema = new Schema<IOrganicOrder>({
  affId: { type: String, required: true },
  offerId: { type: String, required: true },
  orderInfo: {
    orderNumber: { type: String, required: true },
    clickEvent: { type: String, required: true },
    conversionEvent: {
      transaction_id: { type: String, required: true },
      conversion_id: { type: String, required: true }
    },
    reffererUrl: { type: String, required: true },
    url: { type: String, required: true }
  },
  isDeleted: { type: Boolean, default: false },
}, { 
  timestamps: true,
  collection: 'organicorders'
})

// Indexes
OrganicOrderSchema.index({ affId: 1, offerId: 1 })
OrganicOrderSchema.index({ isDeleted: 1 })
OrganicOrderSchema.index({ 'orderInfo.orderNumber': 1 })

export default mongoose.models.OrganicOrder || mongoose.model<IOrganicOrder>('OrganicOrder', OrganicOrderSchema)
