import mongoose, { Document, Schema } from 'mongoose'

export interface IAlternateAffiliate extends Document {
  altAffId: string
  rate: number
  totalOrdersFired: number
  startDate: Date
  endDate: Date
  isPaused: boolean
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
}

const AlternateAffiliateSchema = new Schema<IAlternateAffiliate>({
  altAffId: { type: String, required: true },
  rate: { type: Number, default: 0, required: true },
  totalOrdersFired: { type: Number, default: 0, required: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  isPaused: { type: Boolean, default: false },
  isDeleted: { type: Boolean, default: false },
}, { 
  timestamps: true,
  collection: 'alternateaffiliates'
})

// Indexes
AlternateAffiliateSchema.index({ altAffId: 1 })
AlternateAffiliateSchema.index({ isDeleted: 1 })
AlternateAffiliateSchema.index({ isPaused: 1 })

export default mongoose.models.AlternateAffiliate || mongoose.model<IAlternateAffiliate>('AlternateAffiliate', AlternateAffiliateSchema)
