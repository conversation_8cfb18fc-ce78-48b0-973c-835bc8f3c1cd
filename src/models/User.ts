import mongoose, { Document, Schema } from 'mongoose'

export interface I<PERSON>ser extends Document {
  email: string
  password: string
  role: string
  firstName: string
  lastName: string
  tokenLogin: string
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
}

const UserSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, required: true, default: 'user' },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  tokenLogin: { type: String, default: '' },
  isDeleted: { type: Boolean, default: false },
}, { 
  timestamps: true,
  collection: 'users'
})

// Indexes
UserSchema.index({ email: 1 })
UserSchema.index({ isDeleted: 1 })
UserSchema.index({ tokenLogin: 1 })

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema)
