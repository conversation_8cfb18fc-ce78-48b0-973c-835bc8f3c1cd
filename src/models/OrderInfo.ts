import mongoose, { Document, Schema } from 'mongoose'

export interface ICustomerInfo {
  email?: string
  firstName?: string
  lastName?: string
  phone?: string
}

export interface IOrderInfo extends Document {
  orderNumber: string
  affId: string
  offerId: string
  amount: number
  currency: string
  status: 'pending' | 'confirmed' | 'cancelled' | 'refunded'
  customerInfo?: ICustomerInfo
  metadata?: Record<string, unknown>
  processedAt?: Date
  confirmedAt?: Date
  cancelledAt?: Date
  refundedAt?: Date
  createdAt: Date
  updatedAt: Date
}

const CustomerInfoSchema = new Schema<ICustomerInfo>({
  email: { type: String },
  firstName: { type: String },
  lastName: { type: String },
  phone: { type: String },
}, { _id: false })

const OrderInfoSchema = new Schema<IOrderInfo>({
  orderNumber: { type: String, required: true, unique: true },
  affId: { type: String, required: true },
  offerId: { type: String, required: true },
  amount: { type: Number, required: true, min: 0 },
  currency: { type: String, required: true, default: 'USD' },
  status: { 
    type: String, 
    required: true, 
    enum: ['pending', 'confirmed', 'cancelled', 'refunded'],
    default: 'pending'
  },
  customerInfo: { type: CustomerInfoSchema },
  metadata: { type: Schema.Types.Mixed },
  processedAt: { type: Date },
  confirmedAt: { type: Date },
  cancelledAt: { type: Date },
  refundedAt: { type: Date },
}, { 
  timestamps: true,
  collection: 'orderinfos'
})

// Indexes
OrderInfoSchema.index({ orderNumber: 1 })
OrderInfoSchema.index({ affId: 1, offerId: 1 })
OrderInfoSchema.index({ status: 1 })
OrderInfoSchema.index({ createdAt: -1 })
OrderInfoSchema.index({ 'customerInfo.email': 1 })

// Pre-save middleware to set status timestamps
OrderInfoSchema.pre('save', function(next) {
  const now = new Date()
  
  if (this.isModified('status')) {
    switch (this.status) {
      case 'confirmed':
        if (!this.confirmedAt) this.confirmedAt = now
        break
      case 'cancelled':
        if (!this.cancelledAt) this.cancelledAt = now
        break
      case 'refunded':
        if (!this.refundedAt) this.refundedAt = now
        break
    }
    
    if (!this.processedAt && this.status !== 'pending') {
      this.processedAt = now
    }
  }
  
  next()
})

export default mongoose.models.OrderInfo || mongoose.model<IOrderInfo>('OrderInfo', OrderInfoSchema)
