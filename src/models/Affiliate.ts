import mongoose, { Document, Schema } from 'mongoose'

export interface IAltAff {
  altAffId: string
  rate: number
  isPaused: boolean
}

export interface IAffiliate extends Document {
  affId: string
  offerId: string
  altAffId?: string
  altAffs?: IAltAff[]
  rate: number
  orders: number
  total_not_fired: number
  startDate: Date
  endDate: Date
  userEmail: string
  isPaused: boolean
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
}

const AltAffSchema = new Schema({
  altAffId: { type: String, required: true },
  rate: { type: Number, required: true },
  isPaused: { type: Boolean, default: false }
}, { _id: false })

const AffiliateSchema = new Schema<IAffiliate>({
  affId: { type: String, required: true },
  offerId: { type: String, required: true },
  altAffId: { type: String, default: null },
  altAffs: { type: [AltAffSchema], default: [] },
  rate: { type: Number, default: 0, required: true },
  orders: { type: Number, default: 0, required: true },
  total_not_fired: { type: Number, default: 0, required: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  userEmail: { type: String, required: true },
  isPaused: { type: Boolean, default: false },
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: true,
  collection: 'affiliates'
})

// Indexes
AffiliateSchema.index({ affId: 1, offerId: 1 })
AffiliateSchema.index({ isDeleted: 1 })
AffiliateSchema.index({ userEmail: 1 })
AffiliateSchema.index({ isPaused: 1 })

export default mongoose.models.Affiliate || mongoose.model<IAffiliate>('Affiliate', AffiliateSchema)
