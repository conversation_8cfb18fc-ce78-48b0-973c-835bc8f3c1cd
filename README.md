# GPC Everflow - Global Performance Commerce Management System

A modern Next.js application for managing affiliate marketing campaigns and organic sales, migrated from Express.js with enhanced security, performance, and developer experience.

## 🚀 Features

- **Modern Stack**: Next.js 15, TypeScript, Tailwind CSS
- **Authentication**: JWT-based authentication with secure token management
- **Database**: MongoDB with Mongoose ODM
- **Security**: Rate limiting, CORS, security headers, input validation
- **API**: RESTful APIs with comprehensive error handling
- **UI**: Responsive design with shadcn/ui components
- **Deployment**: Docker support with production-ready configuration

## 📋 Prerequisites

- Node.js 18+
- MongoDB 6.0+
- npm or yarn

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gpc-everflow
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your configuration:
   ```env
   MONGODB_URI=mongodb://localhost:27017
   DB_NAME=gpc_everflow
   JWT_SECRET=your-super-secret-jwt-key-here
   NODE_ENV=development
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Protected dashboard pages
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
│   ├── ui/               # Basic UI components
│   ├── forms/            # Form components
│   └── layout/           # Layout components
├── lib/                  # Utilities and configurations
│   ├── auth.ts           # Authentication utilities
│   ├── db.ts             # Database connection
│   ├── utils.ts          # General utilities
│   ├── validations.ts    # Zod schemas
│   └── rate-limit.ts     # Rate limiting
├── models/               # Database models
└── types/                # TypeScript type definitions
```

## 🔐 Authentication

The application uses JWT-based authentication:

- **Login**: `POST /api/auth/signin`
- **Logout**: `POST /api/auth/signout`
- **Register**: `POST /api/auth/register`

Default admin credentials (change in production):
- Email: `<EMAIL>`
- Password: `admin123`

## 📡 API Endpoints

### Authentication
- `POST /api/auth/signin` - User sign in
- `POST /api/auth/signout` - User sign out
- `POST /api/auth/register` - User registration

### Users
- `GET /api/users` - Get all users (paginated)
- `POST /api/users` - Create new user
- `GET /api/users/[id]` - Get user by ID
- `PUT /api/users/[id]` - Update user
- `DELETE /api/users/[id]` - Delete user

### Affiliates
- `GET /api/affiliates` - Get all affiliates (paginated)
- `POST /api/affiliates` - Create new affiliate

### Organic Sales
- `GET /api/organic-sales` - Get all organic sales
- `POST /api/organic-sales` - Create new organic sale

### Serverless Endpoints
- `GET /api/check-and-get-alt-affid` - Check alternate affiliate
- `GET /api/check-organic-sale` - Check organic sale by URL

### System
- `GET /api/health` - Health check
- `GET /api/docs` - API documentation

## 🔒 Security Features

- **Rate Limiting**: Different limits for auth, API, and general endpoints
- **CORS**: Configurable cross-origin resource sharing
- **Security Headers**: XSS protection, content type options, frame options
- **Input Validation**: Zod schema validation for all inputs
- **JWT Security**: Secure token generation and verification
- **Error Handling**: Comprehensive error handling with logging

## 🚀 Deployment

### Docker Deployment

1. **Build and run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

2. **Environment variables for production**
   Update `docker-compose.yml` with production values:
   ```yaml
   environment:
     - NODE_ENV=production
     - MONGODB_URI=mongodb://mongo:27017
     - JWT_SECRET=your-production-secret
     - NEXTAUTH_SECRET=your-nextauth-secret
   ```

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

### Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy**
   ```bash
   vercel --prod
   ```

## 🧪 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run type-check` - Run TypeScript type checking

### Code Quality

- **TypeScript**: Full type safety
- **ESLint**: Code linting with Next.js config
- **Prettier**: Code formatting (configure as needed)

## 📊 Monitoring

- **Health Check**: `/api/health` endpoint for monitoring
- **Logging**: Comprehensive logging with different levels
- **Error Tracking**: Structured error handling and logging

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017` |
| `DB_NAME` | Database name | `gpc_everflow` |
| `JWT_SECRET` | JWT signing secret | Required |
| `NODE_ENV` | Environment | `development` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `*` (dev), specific domains (prod) |

### Rate Limiting

- **General**: 100 requests per 15 minutes
- **Auth**: 5 requests per 15 minutes
- **API**: 60 requests per minute

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team

---

**Note**: This application was migrated from Express.js to Next.js for improved performance, security, and developer experience while maintaining all original functionality.
