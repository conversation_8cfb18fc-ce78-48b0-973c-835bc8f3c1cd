// MongoDB initialization script
db = db.getSiblingDB('gpc_everflow');

// Create collections with indexes
db.createCollection('users');
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ isDeleted: 1 });
db.users.createIndex({ tokenLogin: 1 });

db.createCollection('affiliates');
db.affiliates.createIndex({ affId: 1, offerId: 1 });
db.affiliates.createIndex({ isDeleted: 1 });
db.affiliates.createIndex({ userEmail: 1 });
db.affiliates.createIndex({ isPaused: 1 });

db.createCollection('organicsales');
db.organicsales.createIndex({ pageURL: 1 }, { unique: true });
db.organicsales.createIndex({ isDeleted: 1 });

db.createCollection('alternateaffiliates');
db.alternateaffiliates.createIndex({ altAffId: 1 });
db.alternateaffiliates.createIndex({ isDeleted: 1 });
db.alternateaffiliates.createIndex({ isPaused: 1 });

db.createCollection('organicorders');
db.organicorders.createIndex({ affId: 1, offerId: 1 });
db.organicorders.createIndex({ isDeleted: 1 });
db.organicorders.createIndex({ 'orderInfo.orderNumber': 1 });

// Create default admin user (change password in production!)
const bcrypt = require('bcrypt');
const defaultPassword = '$2b$10$rOzJqZxnTkDjNjYjYjYjYOzJqZxnTkDjNjYjYjYjYOzJqZxnTkDjNj'; // 'admin123'

db.users.insertOne({
  email: '<EMAIL>',
  password: defaultPassword,
  role: 'admin',
  firstName: 'Admin',
  lastName: 'User',
  tokenLogin: '',
  isDeleted: false,
  createdAt: new Date(),
  updatedAt: new Date()
});

print('Database initialized successfully!');
