{"name": "gpc-everflow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "echo \"No tests specified\" && exit 0", "clean": "rm -rf .next out", "analyze": "ANALYZE=true npm run build", "export": "next export"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@radix-ui/react-slot": "^1.2.3", "@types/bcryptjs": "^2.4.6", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "bootstrap": "^5.3.7", "bootstrap-icons": "^1.13.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.1", "cors": "^2.8.5", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mongoose": "^8.16.5", "next": "15.4.4", "next-auth": "^4.24.11", "react": "19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "server-only": "^0.0.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^6.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.96", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}