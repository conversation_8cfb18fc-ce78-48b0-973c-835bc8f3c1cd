#!/bin/bash

# GPC Everflow Deployment Script
# This script helps deploy the application to different environments

set -e

echo "🚀 GPC Everflow Deployment Script"
echo "=================================="

# Check if environment is provided
if [ -z "$1" ]; then
    echo "Usage: ./deploy.sh [development|production|docker]"
    echo ""
    echo "Options:"
    echo "  development  - Start development server"
    echo "  production   - Build and start production server"
    echo "  docker       - Build and run with Docker"
    echo "  docker-prod  - Build and run with Docker Compose"
    exit 1
fi

ENVIRONMENT=$1

case $ENVIRONMENT in
    "development")
        echo "🔧 Starting development environment..."
        echo "Installing dependencies..."
        npm install
        echo "Starting development server..."
        npm run dev
        ;;
    
    "production")
        echo "🏗️  Building for production..."
        echo "Installing dependencies..."
        npm ci --only=production
        echo "Building application..."
        npm run build
        echo "Starting production server..."
        npm start
        ;;
    
    "docker")
        echo "🐳 Building Docker image..."
        docker build -t gpc-everflow .
        echo "Running Docker container..."
        docker run -p 3000:3000 \
            -e NODE_ENV=production \
            -e MONGODB_URI=${MONGODB_URI:-mongodb://host.docker.internal:27017} \
            -e DB_NAME=${DB_NAME:-gpc_everflow} \
            -e JWT_SECRET=${JWT_SECRET:-your-jwt-secret} \
            gpc-everflow
        ;;
    
    "docker-prod")
        echo "🐳 Starting with Docker Compose..."
        if [ ! -f docker-compose.yml ]; then
            echo "❌ docker-compose.yml not found!"
            exit 1
        fi
        echo "Building and starting services..."
        docker-compose up --build -d
        echo "✅ Services started successfully!"
        echo "Application: http://localhost:3000"
        echo "MongoDB: localhost:27017"
        echo ""
        echo "To view logs: docker-compose logs -f"
        echo "To stop: docker-compose down"
        ;;
    
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        echo "Available options: development, production, docker, docker-prod"
        exit 1
        ;;
esac

echo ""
echo "✅ Deployment completed for $ENVIRONMENT environment!"

if [ "$ENVIRONMENT" != "docker-prod" ]; then
    echo "🌐 Application should be available at: http://localhost:3000"
fi

echo ""
echo "📋 Next steps:"
echo "1. Check application health: curl http://localhost:3000/api/health"
echo "2. View API documentation: http://localhost:3000/api/docs"
echo "3. Access dashboard: http://localhost:3000/dashboard"
echo ""
echo "🔐 Default admin credentials (change in production):"
echo "Email: <EMAIL>"
echo "Password: admin123"
