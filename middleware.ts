import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { applyRateLimit, generalLimiter, authLimiter, apiLimiter } from './src/lib/rate-limit'

export function middleware(request: NextRequest) {
  // Temporarily disable all middleware logic to debug redirect loops
  console.log('Middleware called for:', request.nextUrl.pathname)

  // Just return next() without any processing
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
